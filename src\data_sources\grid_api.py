"""
GRID Open Access API integration for CS2 data
"""
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import json

from src.models.match import Match, MatchFormat, MatchOdds
from src.models.team import TeamStats, PlayerStats
from src.utils.logger import get_logger
from config.settings import settings

logger = get_logger("GridAPI")


class GridAPI:
    """GRID Open Access API client for CS2 data"""
    
    def __init__(self):
        self.base_url = settings.grid_api_base_url
        self.api_key = settings.grid_api_key
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        headers = {}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=settings.request_timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict]:
        """Make API request with error handling"""
        if not self.api_key:
            logger.warning("GRID API key not configured")
            return None
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 401:
                    logger.error("GRID API authentication failed")
                elif response.status == 429:
                    logger.warning("GRID API rate limit exceeded")
                else:
                    logger.warning(f"GRID API returned status {response.status}")
                    
        except Exception as e:
            logger.error(f"GRID API request failed: {e}")
        
        return None
    
    async def get_upcoming_matches(self, target_date: datetime = None) -> List[Match]:
        """
        Get upcoming CS2 matches from GRID API
        
        Args:
            target_date: Date to filter matches for (default: today)
            
        Returns:
            List of Match objects
        """
        if target_date is None:
            target_date = datetime.now().date()
        
        logger.info(f"Fetching GRID matches for {target_date}")
        
        # GRID API parameters for CS2 matches
        params = {
            'game': 'cs2',
            'status': 'upcoming',
            'date_from': target_date.isoformat(),
            'date_to': (target_date + timedelta(days=1)).isoformat(),
            'limit': 100
        }
        
        data = await self._make_request('/matches', params)
        
        if not data:
            logger.error("Failed to fetch matches from GRID API")
            return []
        
        matches = []
        
        try:
            for match_data in data.get('data', []):
                match = self._parse_match_data(match_data)
                if match:
                    matches.append(match)
            
            logger.info(f"Found {len(matches)} matches from GRID API")
            
        except Exception as e:
            logger.error(f"Error parsing GRID matches: {e}")
        
        return matches
    
    def _parse_match_data(self, match_data: Dict) -> Optional[Match]:
        """Parse match data from GRID API response"""
        try:
            # Extract basic match info
            match_id = f"grid_{match_data.get('id')}"
            
            # Get teams
            teams = match_data.get('teams', [])
            if len(teams) < 2:
                return None
            
            team1 = teams[0].get('name', 'Unknown')
            team2 = teams[1].get('name', 'Unknown')
            
            # Get tournament info
            tournament_data = match_data.get('tournament', {})
            tournament = tournament_data.get('name', 'Unknown Tournament')
            
            # Parse scheduled time
            scheduled_time_str = match_data.get('scheduled_at')
            if scheduled_time_str:
                scheduled_time = datetime.fromisoformat(scheduled_time_str.replace('Z', '+00:00'))
            else:
                scheduled_time = datetime.now() + timedelta(hours=1)
            
            # Parse match format
            format_data = match_data.get('format', {})
            match_format = self._parse_grid_format(format_data)
            
            # Create match object
            match = Match(
                match_id=match_id,
                team1=team1,
                team2=team2,
                tournament=tournament,
                scheduled_time=scheduled_time,
                match_format=match_format,
                team1_hltv_id=teams[0].get('external_ids', {}).get('hltv'),
                team2_hltv_id=teams[1].get('external_ids', {}).get('hltv'),
                data_sources=["grid_api"]
            )
            
            # Parse odds if available
            odds_data = match_data.get('odds', {})
            if odds_data:
                match.odds = self._parse_odds_data(odds_data)
            
            return match
            
        except Exception as e:
            logger.warning(f"Error parsing GRID match data: {e}")
            return None
    
    def _parse_grid_format(self, format_data: Dict) -> MatchFormat:
        """Parse match format from GRID API data"""
        format_type = format_data.get('type', '').lower()
        
        if 'bo5' in format_type or format_data.get('best_of') == 5:
            return MatchFormat.BO5
        elif 'bo3' in format_type or format_data.get('best_of') == 3:
            return MatchFormat.BO3
        else:
            return MatchFormat.BO1
    
    def _parse_odds_data(self, odds_data: Dict) -> MatchOdds:
        """Parse betting odds from GRID API data"""
        odds = MatchOdds(
            source="grid_api",
            last_updated=datetime.now()
        )
        
        try:
            # Parse moneyline odds
            moneyline = odds_data.get('moneyline', {})
            if moneyline:
                odds.team1_moneyline = moneyline.get('team1')
                odds.team2_moneyline = moneyline.get('team2')
            
            # Parse handicap odds
            handicap = odds_data.get('handicap', {})
            if handicap:
                odds.team1_handicap = handicap.get('team1', {})
                odds.team2_handicap = handicap.get('team2', {})
            
            # Parse over/under odds
            totals = odds_data.get('totals', {})
            if totals:
                odds.over_under_maps = totals
                
        except Exception as e:
            logger.warning(f"Error parsing GRID odds data: {e}")
        
        return odds
    
    async def get_team_stats(self, team_name: str, team_id: Optional[str] = None) -> Optional[TeamStats]:
        """
        Get team statistics from GRID API
        
        Args:
            team_name: Name of the team
            team_id: GRID team ID if known
            
        Returns:
            TeamStats object or None
        """
        logger.info(f"Fetching GRID stats for team: {team_name}")
        
        # If we don't have team ID, search for it
        if not team_id:
            team_id = await self._search_team_id(team_name)
            if not team_id:
                logger.warning(f"Could not find GRID team ID for: {team_name}")
                return None
        
        # Get team details
        team_data = await self._make_request(f'/teams/{team_id}')
        if not team_data:
            return None
        
        # Get team statistics
        stats_data = await self._make_request(f'/teams/{team_id}/stats', {
            'game': 'cs2',
            'period': '3M'  # Last 3 months
        })
        
        try:
            team_stats = TeamStats(
                name=team_name,
                data_sources=["grid_api"]
            )
            
            # Parse basic team info
            team_info = team_data.get('data', {})
            team_stats.hltv_id = team_info.get('external_ids', {}).get('hltv')
            
            # Parse statistics
            if stats_data:
                stats = stats_data.get('data', {})
                
                # Win rates
                team_stats.overall_win_rate = stats.get('win_rate')
                team_stats.recent_win_rate = stats.get('recent_win_rate')
                
                # Ratings
                team_stats.average_rating = stats.get('average_rating')
                
                # Map statistics
                map_stats = stats.get('maps', {})
                for map_name, map_data in map_stats.items():
                    team_stats.map_win_rates[map_name] = map_data.get('win_rate', 0)
            
            # Get recent matches
            matches_data = await self._make_request(f'/teams/{team_id}/matches', {
                'game': 'cs2',
                'limit': 10,
                'status': 'finished'
            })
            
            if matches_data:
                team_stats.last_10_matches = matches_data.get('data', [])
            
            logger.info(f"Successfully fetched GRID stats for {team_name}")
            return team_stats
            
        except Exception as e:
            logger.error(f"Error parsing GRID team stats: {e}")
            return None
    
    async def _search_team_id(self, team_name: str) -> Optional[str]:
        """Search for team ID by name"""
        params = {
            'game': 'cs2',
            'name': team_name,
            'limit': 5
        }
        
        data = await self._make_request('/teams/search', params)
        
        if data and data.get('data'):
            # Return the first match
            return data['data'][0].get('id')
        
        return None
    
    async def get_player_stats(self, player_name: str, team_id: Optional[str] = None) -> Optional[PlayerStats]:
        """
        Get player statistics from GRID API
        
        Args:
            player_name: Name of the player
            team_id: Team ID to narrow search
            
        Returns:
            PlayerStats object or None
        """
        logger.info(f"Fetching GRID stats for player: {player_name}")
        
        # Search for player
        params = {
            'game': 'cs2',
            'name': player_name,
            'limit': 5
        }
        
        if team_id:
            params['team_id'] = team_id
        
        data = await self._make_request('/players/search', params)
        
        if not data or not data.get('data'):
            logger.warning(f"Player not found in GRID API: {player_name}")
            return None
        
        player_data = data['data'][0]
        player_id = player_data.get('id')
        
        # Get player statistics
        stats_data = await self._make_request(f'/players/{player_id}/stats', {
            'game': 'cs2',
            'period': '3M'
        })
        
        try:
            player_stats = PlayerStats(
                name=player_name,
                hltv_id=player_data.get('external_ids', {}).get('hltv')
            )
            
            if stats_data:
                stats = stats_data.get('data', {})
                player_stats.rating_2_0 = stats.get('rating_2_0')
                player_stats.adr = stats.get('adr')
                player_stats.kast = stats.get('kast')
                player_stats.impact = stats.get('impact')
            
            return player_stats
            
        except Exception as e:
            logger.error(f"Error parsing GRID player stats: {e}")
            return None
