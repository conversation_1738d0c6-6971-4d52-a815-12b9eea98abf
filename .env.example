# API Keys
GRID_API_KEY=your_grid_api_key_here
PANDASCORE_API_KEY=your_pandascore_api_key_here

# FlareSolverr Configuration
FLARESOLVERR_URL=http://localhost:8191/v1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/cs2_betting.log

# Analysis Configuration
MAX_MATCHES_PER_DAY=20
TARGET_RECOMMENDATIONS=5
MIN_CONFIDENCE_THRESHOLD=60

# Data Source Priorities (1=highest priority)
GRID_API_PRIORITY=1
PANDASCORE_API_PRIORITY=2
HLTV_SCRAPING_PRIORITY=3
BO3_SCRAPING_PRIORITY=4

# Risk Assessment Thresholds
LOW_RISK_RANKING_GAP=10
MEDIUM_RISK_RANKING_GAP=5
LOW_RISK_WIN_RATE=70
MEDIUM_RISK_WIN_RATE=60
MIN_TEAM_RATING=1.05

# Timeouts and Delays
REQUEST_TIMEOUT=30
RATE_LIMIT_DELAY=1
MAX_RETRIES=3
