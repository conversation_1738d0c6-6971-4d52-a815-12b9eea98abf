# CS2 Betting Analysis System

An automated system that identifies and analyzes the top 5 most profitable CS2 betting opportunities for the current day by fetching upcoming matches, gathering detailed team/player statistics, and implementing advanced risk assessment algorithms.

## Features

- **Multi-Source Data Collection**: Integrates with GRID API, HLTV scraping, and Bo3.gg
- **Intelligent Risk Assessment**: Categorizes bets into Low, Medium, and High risk levels
- **Selective Data Fetching**: Only fetches statistics for teams in upcoming matches
- **FlareSolverr Integration**: Bypasses Cloudflare protection for web scraping
- **Advanced Statistical Analysis**: Comprehensive team and player performance metrics
- **Expected Value Calculations**: Focus on positive EV betting opportunities
- **Automated Daily Reports**: Generates ranked list of betting recommendations

## Quick Start

### Prerequisites

- Python 3.8+
- Docker (for FlareSolverr)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cs2-bet-good
   ```

2. **Run the setup script**
   ```bash
   python setup.py
   ```

3. **Configure API keys** (edit `.env` file)
   ```bash
   # API Keys
   GRID_API_KEY=your_grid_api_key_here
   PANDASCORE_API_KEY=your_pandascore_api_key_here
   ```

4. **Run the analysis**
   ```bash
   python main.py
   ```

## Configuration

### Environment Variables

Key configuration options in `.env`:

- `GRID_API_KEY`: GRID Open Access API key
- `PANDASCORE_API_KEY`: PandaScore API key
- `FLARESOLVERR_URL`: FlareSolverr endpoint (default: http://localhost:8191/v1)
- `TARGET_RECOMMENDATIONS`: Number of recommendations to generate (default: 5)
- `MIN_CONFIDENCE_THRESHOLD`: Minimum confidence for recommendations (default: 60)

### Risk Assessment Thresholds

- **Low Risk Criteria**:
  - HLTV ranking gap ≥ 10 positions
  - Favored team win rate ≥ 70% in last 10 matches
  - BO3/BO5 format only
  - Map pool advantage (≥ 65% win rate on 2+ maps)
  - No roster changes in last 30 days
  - Team average rating ≥ 1.10

- **Medium Risk Criteria**:
  - HLTV ranking gap ≥ 5 positions
  - Favored team win rate ≥ 60% in last 10 matches
  - BO3 preferred (BO1 with exceptional justification)
  - Map pool advantage on at least 1 map
  - Team average rating ≥ 1.05

## Data Sources

### Primary Sources (APIs)
1. **GRID Open Access** - Official esports data API
2. **PandaScore API** - Comprehensive esports statistics

### Fallback Sources (Scraping)
3. **HLTV.org** - Premier CS2 statistics and rankings
4. **Bo3.gg** - Match schedules and basic statistics

## Architecture

```
cs2-bet-good/
├── config/                 # Configuration management
├── src/
│   ├── data_sources/       # API integrations
│   ├── scrapers/          # Web scraping modules
│   ├── models/            # Data models
│   ├── analysis/          # Risk assessment and statistics
│   ├── utils/             # Utilities and validation
│   └── reporting/         # Report generation
├── logs/                  # Application logs
├── tests/                 # Unit tests
├── main.py               # Main execution script
└── requirements.txt      # Python dependencies
```

## Usage Examples

### Basic Analysis
```bash
python main.py
```

### Custom Date Analysis
```python
from main import CS2BettingAnalyzer
from datetime import datetime

analyzer = CS2BettingAnalyzer()
report = await analyzer.run_daily_analysis(datetime(2025, 5, 26))
```

### API Integration
```python
from src.data_sources.grid_api import GridAPI

async with GridAPI() as grid:
    matches = await grid.get_upcoming_matches()
    team_stats = await grid.get_team_stats("Team Liquid")
```

## Output Format

The system generates a comprehensive daily report including:

- **Match Details**: Teams, tournament, scheduled time, format
- **Recommended Bet Type**: Moneyline, handicap, over/under, etc.
- **Risk Level**: Low/Medium classification
- **Confidence Percentage**: 60-90% confidence range
- **Expected Value**: Positive EV calculations
- **Statistical Justifications**: Ranking gaps, win rates, map advantages
- **Red Flags**: Potential concerns or risks

### Sample Output
```
CS2 BETTING ANALYSIS REPORT
============================================================
Date: 2025-01-20
Matches Analyzed: 8
Recommendations: 5
Execution Time: 45.2 seconds

TOP RECOMMENDATIONS:

1. Team Liquid vs FURIA
   Tournament: IEM Katowice 2025
   Recommended Bet: Moneyline on Team Liquid
   Risk Level: Low
   Confidence: 78.5%
   Expected Value: 0.156
   Red Flags: None

2. NAVI vs Complexity
   Tournament: BLAST Premier Spring
   Recommended Bet: Moneyline on NAVI
   Risk Level: Medium
   Confidence: 72.1%
   Expected Value: 0.134
   Red Flags: Recent roster change (Complexity)
```

## Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black src/ tests/
flake8 src/ tests/
```

### Adding New Data Sources
1. Create new module in `src/data_sources/` or `src/scrapers/`
2. Implement required methods: `get_upcoming_matches()`, `get_team_stats()`
3. Add to data source priority in `config/settings.py`
4. Update main analyzer to include new source

## Troubleshooting

### Common Issues

1. **FlareSolverr not working**
   ```bash
   docker-compose restart flaresolverr
   ```

2. **API rate limits**
   - Increase `RATE_LIMIT_DELAY` in `.env`
   - Check API key quotas

3. **Missing team statistics**
   - Verify team names match across data sources
   - Check data source availability

4. **No recommendations generated**
   - Lower `MIN_CONFIDENCE_THRESHOLD`
   - Check if matches meet risk criteria
   - Review logs for data quality issues

### Logs

Check application logs in `logs/cs2_betting.log` for detailed debugging information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is for educational and research purposes only. Please ensure compliance with local gambling laws and regulations.

## Disclaimer

This system is for analysis purposes only. Always gamble responsibly and within your means. Past performance does not guarantee future results.
