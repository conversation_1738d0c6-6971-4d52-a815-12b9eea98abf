"""
CS2 Betting Analysis System - Main Execution Script
"""
import asyncio
import sys
from datetime import datetime
from typing import List, Dict, Optional

from src.utils.logger import get_logger, setup_logger
from config.settings import settings, get_data_source_priority, validate_api_keys
from src.data_sources.grid_api import GridAPI
from src.scrapers.hltv_scraper import H<PERSON><PERSON><PERSON>raper
from src.scrapers.bo3_scraper import Bo3<PERSON><PERSON>raper
from src.models.match import Match, DailyReport
from src.models.team import TeamStats
from src.utils.data_validator import DataValidator
from src.analysis.risk_assessment import RiskAssessment

# Setup logging
setup_logger()
logger = get_logger("CS2BettingMain")


class CS2BettingAnalyzer:
    """Main CS2 betting analysis orchestrator"""
    
    def __init__(self):
        self.data_sources = get_data_source_priority()
        self.api_keys = validate_api_keys()
        self.risk_assessor = RiskAssessment()
        self.validator = DataValidator()
        
    async def run_daily_analysis(self, target_date: datetime = None) -> DailyReport:
        """
        Run complete daily betting analysis
        
        Args:
            target_date: Date to analyze (default: today)
            
        Returns:
            DailyReport with recommendations
        """
        if target_date is None:
            target_date = datetime.now()
        
        logger.info(f"Starting CS2 betting analysis for {target_date.date()}")
        start_time = datetime.now()
        
        try:
            # Phase 1: Data Acquisition
            logger.info("Phase 1: Data Acquisition")
            matches = await self._fetch_upcoming_matches(target_date)
            
            if not matches:
                logger.warning("No matches found for analysis")
                return self._create_empty_report(target_date, start_time)
            
            logger.info(f"Found {len(matches)} matches for analysis")
            
            # Phase 2: Team Statistics Collection
            logger.info("Phase 2: Team Statistics Collection")
            team_stats = await self._fetch_team_statistics(matches)
            
            # Phase 3: Data Validation
            logger.info("Phase 3: Data Validation")
            sufficiency_report = self.validator.get_data_sufficiency_report(matches, team_stats)
            
            if sufficiency_report['overall_data_quality'] < 0.3:
                logger.error("Insufficient data quality for reliable analysis")
                return self._create_empty_report(target_date, start_time, 
                                               warnings=["Insufficient data quality"])
            
            # Phase 4: Risk Assessment and Analysis
            logger.info("Phase 4: Risk Assessment and Analysis")
            predictions = await self._analyze_matches(matches, team_stats)
            
            # Phase 5: Ranking and Selection
            logger.info("Phase 5: Ranking and Selection")
            top_recommendations = self._select_top_recommendations(predictions)
            
            # Phase 6: Generate Report
            execution_time = (datetime.now() - start_time).total_seconds()
            
            report = DailyReport(
                date=target_date,
                total_matches_analyzed=len(matches),
                recommendations=top_recommendations[:settings.target_recommendations],
                alternative_bets=top_recommendations[settings.target_recommendations:],
                avg_confidence=self._calculate_avg_confidence(top_recommendations),
                avg_expected_value=self._calculate_avg_ev(top_recommendations),
                risk_distribution=self._calculate_risk_distribution(top_recommendations),
                execution_time_seconds=execution_time,
                data_sources_used=list(set([source for match in matches for source in match.data_sources])),
                warnings=sufficiency_report.get('recommendations', [])
            )
            
            logger.info(f"Analysis completed in {execution_time:.2f} seconds")
            logger.info(f"Generated {len(report.recommendations)} recommendations")
            
            return report
            
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return self._create_empty_report(target_date, start_time, 
                                           warnings=[f"Analysis failed: {str(e)}"])
    
    async def _fetch_upcoming_matches(self, target_date: datetime) -> List[Match]:
        """Fetch upcoming matches from available data sources"""
        all_matches = []
        
        for source in self.data_sources:
            try:
                logger.info(f"Fetching matches from {source}")
                
                if source == "grid_api" and self.api_keys["grid_api"]:
                    async with GridAPI() as grid:
                        matches = await grid.get_upcoming_matches(target_date)
                        all_matches.extend(matches)
                        
                elif source == "hltv_scraping":
                    async with HLTVScraper() as hltv:
                        matches = await hltv.get_upcoming_matches(target_date)
                        all_matches.extend(matches)
                        
                elif source == "bo3_scraping":
                    async with Bo3Scraper() as bo3:
                        matches = await bo3.get_upcoming_matches(target_date)
                        all_matches.extend(matches)
                
                logger.info(f"Fetched {len(matches) if 'matches' in locals() else 0} matches from {source}")
                
            except Exception as e:
                logger.error(f"Failed to fetch matches from {source}: {e}")
                continue
        
        # Remove duplicates based on team names and time
        unique_matches = self._deduplicate_matches(all_matches)
        logger.info(f"Total unique matches after deduplication: {len(unique_matches)}")
        
        return unique_matches
    
    def _deduplicate_matches(self, matches: List[Match]) -> List[Match]:
        """Remove duplicate matches from different sources"""
        seen = set()
        unique_matches = []
        
        for match in matches:
            # Create a key based on teams and approximate time
            key = (
                tuple(sorted([match.team1.lower(), match.team2.lower()])),
                match.scheduled_time.date()
            )
            
            if key not in seen:
                seen.add(key)
                unique_matches.append(match)
            else:
                # Merge data sources for existing match
                for existing_match in unique_matches:
                    existing_key = (
                        tuple(sorted([existing_match.team1.lower(), existing_match.team2.lower()])),
                        existing_match.scheduled_time.date()
                    )
                    if existing_key == key:
                        existing_match.data_sources.extend(match.data_sources)
                        break
        
        return unique_matches
    
    async def _fetch_team_statistics(self, matches: List[Match]) -> Dict[str, TeamStats]:
        """Fetch statistics for all teams in matches"""
        team_stats = {}
        unique_teams = set()
        
        # Collect unique team names
        for match in matches:
            unique_teams.add(match.team1)
            unique_teams.add(match.team2)
        
        logger.info(f"Fetching statistics for {len(unique_teams)} unique teams")
        
        for team_name in unique_teams:
            try:
                stats = await self._fetch_single_team_stats(team_name)
                if stats:
                    team_stats[team_name] = stats
                else:
                    logger.warning(f"Could not fetch stats for team: {team_name}")
                    
            except Exception as e:
                logger.error(f"Error fetching stats for {team_name}: {e}")
        
        logger.info(f"Successfully fetched stats for {len(team_stats)} teams")
        return team_stats
    
    async def _fetch_single_team_stats(self, team_name: str) -> Optional[TeamStats]:
        """Fetch statistics for a single team from available sources"""
        
        for source in self.data_sources:
            try:
                if source == "grid_api" and self.api_keys["grid_api"]:
                    async with GridAPI() as grid:
                        stats = await grid.get_team_stats(team_name)
                        if stats:
                            return stats
                            
                elif source == "hltv_scraping":
                    async with HLTVScraper() as hltv:
                        stats = await hltv.get_team_stats(team_name)
                        if stats:
                            return stats
                
            except Exception as e:
                logger.warning(f"Failed to fetch {team_name} stats from {source}: {e}")
                continue
        
        return None
    
    async def _analyze_matches(self, matches: List[Match], team_stats: Dict[str, TeamStats]) -> List:
        """Analyze matches and generate predictions"""
        predictions = []
        
        for match in matches:
            try:
                team1_stats = team_stats.get(match.team1)
                team2_stats = team_stats.get(match.team2)
                
                if not team1_stats or not team2_stats:
                    logger.warning(f"Missing team stats for match: {match.team1} vs {match.team2}")
                    continue
                
                # Create team comparison
                team_comparison = self._create_team_comparison(team1_stats, team2_stats)
                
                # Assess risk
                risk_level, assessment_details, red_flags = self.risk_assessor.assess_match_risk(
                    match, team1_stats, team2_stats, team_comparison
                )
                
                # Skip high-risk matches
                if risk_level.value == "High":
                    continue
                
                # Create prediction (simplified for now)
                prediction = self._create_match_prediction(
                    match, team1_stats, team2_stats, team_comparison,
                    risk_level, assessment_details, red_flags
                )
                
                if prediction:
                    predictions.append(prediction)
                    
            except Exception as e:
                logger.error(f"Error analyzing match {match.match_id}: {e}")
                continue
        
        return predictions
    
    def _create_team_comparison(self, team1_stats: TeamStats, team2_stats: TeamStats):
        """Create team comparison object (simplified implementation)"""
        from src.models.team import TeamComparison
        
        ranking_gap = None
        if team1_stats.hltv_ranking and team2_stats.hltv_ranking:
            ranking_gap = abs(team1_stats.hltv_ranking - team2_stats.hltv_ranking)
        
        win_rate_diff = None
        if team1_stats.recent_win_rate and team2_stats.recent_win_rate:
            win_rate_diff = abs(team1_stats.recent_win_rate - team2_stats.recent_win_rate)
        
        return TeamComparison(
            team1=team1_stats,
            team2=team2_stats,
            ranking_gap=ranking_gap,
            win_rate_difference=win_rate_diff
        )
    
    def _create_match_prediction(self, match, team1_stats, team2_stats, team_comparison, 
                               risk_level, assessment_details, red_flags):
        """Create match prediction (simplified implementation)"""
        from src.models.match import MatchPrediction, BetType
        
        # Simplified prediction logic
        favored_team = assessment_details.get('favored_team', match.team1)
        confidence = 70 if risk_level.value == "Low" else 65
        expected_value = 0.15 if risk_level.value == "Low" else 0.08
        
        return MatchPrediction(
            match=match,
            recommended_bet_type=BetType.MONEYLINE,
            recommended_team=favored_team,
            confidence_percentage=confidence,
            expected_value=expected_value,
            risk_level=risk_level,
            ranking_gap=team_comparison.ranking_gap,
            win_rate_difference=team_comparison.win_rate_difference,
            red_flags=red_flags,
            key_stats=assessment_details,
            data_citations=[f"Analysis performed on {datetime.now().isoformat()}"]
        )
    
    def _select_top_recommendations(self, predictions) -> List:
        """Select and rank top betting recommendations"""
        # Sort by expected value and confidence
        sorted_predictions = sorted(
            predictions,
            key=lambda p: (p.expected_value, p.confidence_percentage),
            reverse=True
        )
        
        return sorted_predictions
    
    def _calculate_avg_confidence(self, predictions) -> float:
        """Calculate average confidence of predictions"""
        if not predictions:
            return 0.0
        return sum(p.confidence_percentage for p in predictions) / len(predictions)
    
    def _calculate_avg_ev(self, predictions) -> float:
        """Calculate average expected value of predictions"""
        if not predictions:
            return 0.0
        return sum(p.expected_value for p in predictions) / len(predictions)
    
    def _calculate_risk_distribution(self, predictions) -> Dict[str, int]:
        """Calculate risk level distribution"""
        distribution = {"Low": 0, "Medium": 0, "High": 0}
        for prediction in predictions:
            distribution[prediction.risk_level.value] += 1
        return distribution
    
    def _create_empty_report(self, target_date: datetime, start_time: datetime, 
                           warnings: List[str] = None) -> DailyReport:
        """Create empty report when no recommendations are generated"""
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return DailyReport(
            date=target_date,
            total_matches_analyzed=0,
            recommendations=[],
            alternative_bets=[],
            avg_confidence=0.0,
            avg_expected_value=0.0,
            risk_distribution={"Low": 0, "Medium": 0, "High": 0},
            execution_time_seconds=execution_time,
            data_sources_used=[],
            warnings=warnings or []
        )


async def main():
    """Main entry point"""
    logger.info("Starting CS2 Betting Analysis System")
    
    analyzer = CS2BettingAnalyzer()
    
    # Run analysis for today
    report = await analyzer.run_daily_analysis()
    
    # Print results
    print("\n" + "="*60)
    print("CS2 BETTING ANALYSIS REPORT")
    print("="*60)
    print(f"Date: {report.date.date()}")
    print(f"Matches Analyzed: {report.total_matches_analyzed}")
    print(f"Recommendations: {len(report.recommendations)}")
    print(f"Execution Time: {report.execution_time_seconds:.2f} seconds")
    
    if report.recommendations:
        print("\nTOP RECOMMENDATIONS:")
        for i, pred in enumerate(report.recommendations, 1):
            print(f"\n{i}. {pred.match.team1} vs {pred.match.team2}")
            print(f"   Tournament: {pred.match.tournament}")
            print(f"   Recommended Bet: {pred.recommended_bet_type.value} on {pred.recommended_team}")
            print(f"   Risk Level: {pred.risk_level.value}")
            print(f"   Confidence: {pred.confidence_percentage:.1f}%")
            print(f"   Expected Value: {pred.expected_value:.3f}")
            if pred.red_flags:
                print(f"   Red Flags: {', '.join(pred.red_flags)}")
    else:
        print("\nNo betting recommendations generated.")
        if report.warnings:
            print("Warnings:")
            for warning in report.warnings:
                print(f"  - {warning}")


if __name__ == "__main__":
    asyncio.run(main())
