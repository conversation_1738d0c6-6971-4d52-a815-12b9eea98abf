"""
HLTV scraper for CS2 match and team data
"""
import asyncio
import aiohttp
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import time

from src.models.match import Match, MatchFormat
from src.models.team import TeamStats, PlayerStats
from src.utils.logger import get_logger
from config.settings import settings

logger = get_logger("HLTVScraper")


class HLTVScraper:
    """Scraper for HLTV.org data with FlareSolverr support"""

    def __init__(self):
        self.base_url = "https://www.hltv.org"
        self.session = None
        self.use_flaresolverr = True

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=settings.request_timeout)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    def _make_flaresolverr_request(self, url: str) -> Optional[str]:
        """Make request through FlareSolverr to bypass Cloudflare"""
        try:
            flaresolverr_payload = {
                "cmd": "request.get",
                "url": url,
                "maxTimeout": 60000
            }

            response = requests.post(
                settings.flaresolverr_url,
                json=flaresolverr_payload,
                timeout=settings.request_timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "ok":
                    return result["solution"]["response"]
                else:
                    logger.error(f"FlareSolverr error: {result.get('message')}")

        except Exception as e:
            logger.error(f"FlareSolverr request failed: {e}")

        return None

    def _clean_team_name(self, team_name: str) -> str:
        """Clean team name by removing common parsing artifacts"""
        if not team_name:
            return ""

        import re

        # Remove trailing numbers that are likely parsing artifacts
        cleaned = re.sub(r'\d+$', '', team_name).strip()

        # Remove common suffixes that might be parsing artifacts
        suffixes_to_remove = ['fe', 'academy', 'jr', 'young']
        for suffix in suffixes_to_remove:
            if cleaned.lower().endswith(f' {suffix}'):
                # Only remove if it's not part of the actual team name
                if suffix not in team_name.lower().split()[:-1]:
                    cleaned = cleaned[:-len(suffix)-1].strip()

        # Handle special cases
        if cleaned.lower() == 'sashi':
            return 'SaShi'
        elif cleaned.lower() == 'vitality':
            return 'Vitality'
        elif cleaned.lower() == 'astrum':
            return 'Astrum'
        elif cleaned.lower() == 'furia':
            return 'FURIA'
        elif cleaned.lower() == 'og':
            return 'OG'

        return cleaned if len(cleaned) > 1 else team_name

    def _parse_match_format(self, format_text: str) -> MatchFormat:
        """Parse match format from text"""
        format_text = format_text.upper()
        if "BO5" in format_text:
            return MatchFormat.BO5
        elif "BO3" in format_text:
            return MatchFormat.BO3
        else:
            return MatchFormat.BO1

    def _parse_datetime(self, date_str: str, time_str: str) -> Optional[datetime]:
        """Parse HLTV datetime format"""
        try:
            # HLTV uses various date formats, this is a simplified parser
            # In production, you'd need more robust date parsing
            combined = f"{date_str} {time_str}"
            return datetime.strptime(combined, "%Y-%m-%d %H:%M")
        except Exception as e:
            logger.warning(f"Failed to parse datetime: {date_str} {time_str} - {e}")
            return None

    async def get_upcoming_matches(self, target_date: Optional[datetime] = None) -> List[Match]:
        """
        Scrape upcoming matches from HLTV

        Args:
            target_date: Date to filter matches for (default: today)

        Returns:
            List of Match objects
        """
        if target_date is None:
            target_date = datetime.now()

        logger.info(f"Fetching HLTV matches for {target_date}")

        # Format date for HLTV URL
        date_str = target_date.strftime("%Y-%m-%d")
        matches_url = f"{self.base_url}/matches?date={date_str}"
        html_content = self._make_flaresolverr_request(matches_url)

        if not html_content:
            logger.error("Failed to fetch HLTV matches page")
            return []

        soup = BeautifulSoup(html_content, 'html.parser')
        matches = []

        try:
            # Try multiple selectors for HLTV match containers
            match_containers = (
                soup.find_all('div', class_='upcomingMatch') or
                soup.find_all('div', class_='upcoming-match') or
                soup.find_all('div', class_='match-row') or
                soup.find_all('a', class_='a-reset') or
                soup.find_all('div', {'data-zonedgrouping-entry-unix': True})
            )

            logger.info(f"Found {len(match_containers)} potential match containers")

            for container in match_containers:
                try:
                    match = self._parse_match_container(container, target_date)
                    if match:
                        matches.append(match)
                        logger.debug(f"Successfully parsed match: {match.team1} vs {match.team2}")
                except Exception as e:
                    logger.debug(f"Failed to parse match container: {e}")
                    continue

            # If no matches found with standard selectors, try alternative parsing
            if not matches:
                matches = self._parse_alternative_structure(soup, target_date)

            logger.info(f"Found {len(matches)} matches for {target_date}")

        except Exception as e:
            logger.error(f"Error parsing HLTV matches: {e}")

        return matches

    def _parse_alternative_structure(self, soup, target_date) -> List[Match]:
        """Alternative parsing method for HLTV structure"""
        matches = []

        try:
            # Look for specific HLTV match structures
            import re

            # Find all divs that might contain match information
            all_divs = soup.find_all('div')

            # Look for team names in various structures
            potential_matches = []

            # Strategy 1: Look for team links with /team/ in href
            team_links = soup.find_all('a', href=re.compile(r'/team/'))
            team_names = []
            for link in team_links:
                team_name = link.get_text().strip()
                if team_name and len(team_name) > 1 and team_name not in team_names:
                    team_names.append(team_name)

            # Strategy 2: Look for common CS2 team names in text
            text_content = soup.get_text()

            # Known CS2 team patterns (from the screenshot)
            known_teams = [
                '9INE', 'Astrum', 'PARIVISION', 'SaShi', 'HEROIC Academy', 'Preasy',
                'Volt', 'Wildcard Academy', 'UNITY', 'Kubix', 'Iberian Soul', 'Passion UA',
                'RUSH B', 'Fisher College', 'BIG EQUIPA', 'Zero Tenacity', 'SINNERS',
                'SPARTA', 'ECLOT', 'Monte', 'Spirit Academy', 'ENCE', 'Partizan', 'Delta',
                'B8', 'PanicAtTheVeto'
            ]

            # Find team names in the text
            found_teams = []
            for team in known_teams:
                if team in text_content:
                    found_teams.append(team)

            # Add teams from links, but clean them first
            for team_name in team_names[:20]:  # Limit to avoid too many
                # Clean team names - remove numbers and common suffixes
                cleaned_name = self._clean_team_name(team_name)
                if cleaned_name and cleaned_name not in found_teams:
                    found_teams.append(cleaned_name)

            # Remove duplicates while preserving order
            unique_teams = []
            for team in found_teams:
                cleaned_team = self._clean_team_name(team)
                if cleaned_team and cleaned_team not in unique_teams:
                    unique_teams.append(cleaned_team)

            # Create matches by pairing teams
            for i in range(0, len(unique_teams) - 1, 2):
                if i + 1 < len(unique_teams):
                    team1 = unique_teams[i]
                    team2 = unique_teams[i + 1]

                    # Skip if team names are too similar (likely parsing error)
                    if team1.lower() == team2.lower():
                        continue

                    # Create scheduled time (spread matches throughout the day)
                    hour_offset = (i // 2) % 24
                    if isinstance(target_date, datetime):
                        base_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
                    else:
                        base_time = datetime.combine(target_date, datetime.min.time())

                    scheduled_time = base_time + timedelta(hours=hour_offset)

                    match = Match(
                        match_id=f"hltv_alt_{team1.replace(' ', '_')}_{team2.replace(' ', '_')}_{int(time.time())}_{i}",
                        team1=team1,
                        team2=team2,
                        tournament="HLTV Tournament",
                        scheduled_time=scheduled_time,
                        match_format=MatchFormat.BO3,
                        data_sources=["hltv_scraper"]
                    )
                    matches.append(match)

                    if len(matches) >= 15:  # Limit to 15 matches
                        break

        except Exception as e:
            logger.warning(f"Alternative parsing failed: {e}")

        return matches

    def _parse_match_container(self, container, target_date) -> Optional[Match]:
        """Parse individual match container from HLTV"""
        try:
            # Multiple strategies to extract team names
            team1, team2 = None, None

            # Strategy 1: Look for team divs
            team_elements = container.find_all('div', class_='team')
            if len(team_elements) >= 2:
                team1_elem = team_elements[0].find('div', class_='teamName')
                team2_elem = team_elements[1].find('div', class_='teamName')
                if team1_elem and team2_elem:
                    team1 = team1_elem.text.strip()
                    team2 = team2_elem.text.strip()

            # Strategy 2: Look for team names in different structure
            if not team1 or not team2:
                team_links = container.find_all('a', href=lambda x: x and '/team/' in str(x))
                if len(team_links) >= 2:
                    team1 = team_links[0].text.strip()
                    team2 = team_links[1].text.strip()

            # Strategy 3: Look for any text that might be team names
            if not team1 or not team2:
                all_text = container.get_text()
                # Look for patterns like "TeamA vs TeamB" or "TeamA TeamB"
                import re
                vs_pattern = r'([A-Za-z0-9\s]+?)\s+vs\s+([A-Za-z0-9\s]+)'
                match_vs = re.search(vs_pattern, all_text)
                if match_vs:
                    team1 = match_vs.group(1).strip()
                    team2 = match_vs.group(2).strip()

            if not team1 or not team2:
                return None

            # Clean team names
            team1 = self._clean_team_name(team1)
            team2 = self._clean_team_name(team2)

            if not team1 or not team2:
                return None

            # Extract match time
            time_element = (
                container.find('div', class_='matchTime') or
                container.find('div', class_='time') or
                container.find('span', class_='time')
            )

            # Extract tournament
            event_element = (
                container.find('div', class_='matchEvent') or
                container.find('div', class_='event') or
                container.find('span', class_='event')
            )
            tournament = event_element.text.strip() if event_element else "HLTV Tournament"

            # Extract match format (BO1/BO3/BO5)
            format_element = (
                container.find('div', class_='matchMeta') or
                container.find('div', class_='format') or
                container.find('span', class_='format')
            )
            match_format = MatchFormat.BO3  # Default to BO3
            if format_element:
                format_text = format_element.text
                match_format = self._parse_match_format(format_text)

            # Create match ID
            match_id = f"hltv_{team1.replace(' ', '_')}_{team2.replace(' ', '_')}_{int(time.time())}"

            # Parse scheduled time
            scheduled_time = datetime.now() + timedelta(hours=1)  # Default
            if time_element:
                time_text = time_element.text.strip()
                parsed_time = self._parse_hltv_time(time_text, target_date)
                if parsed_time:
                    scheduled_time = parsed_time

            match = Match(
                match_id=match_id,
                team1=team1,
                team2=team2,
                tournament=tournament,
                scheduled_time=scheduled_time,
                match_format=match_format,
                data_sources=["hltv_scraper"]
            )

            return match

        except Exception as e:
            logger.debug(f"Error parsing match container: {e}")
            return None

    def _parse_hltv_time(self, time_text: str, target_date) -> Optional[datetime]:
        """Parse time from HLTV format"""
        try:
            # Handle formats like "04:00", "07:00", etc.
            import re
            time_match = re.search(r'(\d{1,2}):(\d{2})', time_text)
            if time_match:
                hour = int(time_match.group(1))
                minute = int(time_match.group(2))

                # Create datetime for target date
                if isinstance(target_date, datetime):
                    base_date = target_date.date()
                else:
                    base_date = target_date

                scheduled_time = datetime.combine(base_date, datetime.min.time())
                scheduled_time = scheduled_time.replace(hour=hour, minute=minute)

                return scheduled_time

        except Exception as e:
            logger.debug(f"Failed to parse HLTV time '{time_text}': {e}")

        return None

    async def get_team_stats(self, team_name: str, hltv_id: Optional[int] = None) -> Optional[TeamStats]:
        """
        Get detailed team statistics from HLTV

        Args:
            team_name: Name of the team
            hltv_id: HLTV team ID if known

        Returns:
            TeamStats object or None
        """
        logger.info(f"Fetching HLTV stats for team: {team_name}")

        # If we don't have HLTV ID, we'd need to search for it first
        if not hltv_id:
            hltv_id = await self._search_team_id(team_name)
            if not hltv_id:
                logger.warning(f"Could not find HLTV ID for team: {team_name}")
                return None

        team_url = f"{self.base_url}/team/{hltv_id}/{team_name.replace(' ', '-').lower()}"
        html_content = self._make_flaresolverr_request(team_url)

        if not html_content:
            logger.error(f"Failed to fetch team page for {team_name}")
            return None

        soup = BeautifulSoup(html_content, 'html.parser')

        try:
            team_stats = TeamStats(
                name=team_name,
                hltv_id=hltv_id,
                data_sources=["hltv_scraper"]
            )

            # Parse ranking
            ranking_element = soup.find('div', class_='profile-team-stat')
            if ranking_element:
                ranking_text = ranking_element.text
                # Extract ranking number from text like "#5"
                import re
                ranking_match = re.search(r'#(\d+)', ranking_text)
                if ranking_match:
                    team_stats.hltv_ranking = int(ranking_match.group(1))

            # Parse recent matches for win rate calculation
            matches_section = soup.find('div', class_='recent-results')
            if matches_section:
                recent_matches = self._parse_recent_matches(matches_section)
                team_stats.last_10_matches = recent_matches[:10]

                # Calculate win rate
                if recent_matches:
                    wins = sum(1 for match in recent_matches if match.get('result') == 'win')
                    team_stats.recent_win_rate = (wins / len(recent_matches)) * 100

            # Parse player roster
            roster_section = soup.find('div', class_='bodyshot-team')
            if roster_section:
                players = self._parse_team_roster(roster_section)
                team_stats.players = players

                # Calculate average rating
                ratings = [p.rating_2_0 for p in players if p.rating_2_0]
                if ratings:
                    team_stats.average_rating = sum(ratings) / len(ratings)

            logger.info(f"Successfully parsed stats for {team_name}")
            return team_stats

        except Exception as e:
            logger.error(f"Error parsing team stats for {team_name}: {e}")
            return None

    async def _search_team_id(self, team_name: str) -> Optional[int]:
        """Search for team HLTV ID by name"""
        # This would implement team search functionality
        # For now, return None to indicate ID not found
        logger.warning(f"Team ID search not implemented for: {team_name}")
        return None

    def _parse_recent_matches(self, matches_section) -> List[Dict[str, Any]]:
        """Parse recent matches from team page"""
        matches = []
        try:
            match_elements = matches_section.find_all('div', class_='result-con')
            for element in match_elements:
                # Parse match result
                result_element = element.find('span', class_='result-score')
                if result_element:
                    # Simplified parsing - in production you'd extract more details
                    matches.append({
                        'result': 'win' if 'won' in element.get('class', []) else 'loss',
                        'score': result_element.text.strip()
                    })
        except Exception as e:
            logger.warning(f"Error parsing recent matches: {e}")

        return matches

    def _parse_team_roster(self, roster_section) -> List[PlayerStats]:
        """Parse team roster from team page"""
        players = []
        try:
            player_elements = roster_section.find_all('div', class_='playerCol')
            for element in player_elements:
                name_element = element.find('div', class_='player-nick')
                if name_element:
                    player_name = name_element.text.strip()

                    # Create basic player stats (in production, you'd fetch detailed stats)
                    player = PlayerStats(
                        name=player_name,
                        rating_2_0=1.0  # Placeholder - would fetch actual rating
                    )
                    players.append(player)
        except Exception as e:
            logger.warning(f"Error parsing team roster: {e}")

        return players
