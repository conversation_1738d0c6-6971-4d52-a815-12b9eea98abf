"""
HLTV scraper for CS2 match and team data
"""
import asyncio
import aiohttp
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import time

from src.models.match import Match, MatchFormat
from src.models.team import TeamStats, PlayerStats
from src.utils.logger import get_logger
from config.settings import settings

logger = get_logger("HLTVScraper")


class HLTVScraper:
    """Scraper for HLTV.org data with FlareSolverr support"""
    
    def __init__(self):
        self.base_url = "https://www.hltv.org"
        self.session = None
        self.use_flaresolverr = True
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=settings.request_timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _make_flaresolverr_request(self, url: str) -> Optional[str]:
        """Make request through FlareSolverr to bypass Cloudflare"""
        try:
            flaresolverr_payload = {
                "cmd": "request.get",
                "url": url,
                "maxTimeout": 60000
            }
            
            response = requests.post(
                settings.flaresolverr_url,
                json=flaresolverr_payload,
                timeout=settings.request_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "ok":
                    return result["solution"]["response"]
                else:
                    logger.error(f"FlareSolverr error: {result.get('message')}")
            
        except Exception as e:
            logger.error(f"FlareSolverr request failed: {e}")
        
        return None
    
    def _parse_match_format(self, format_text: str) -> MatchFormat:
        """Parse match format from text"""
        format_text = format_text.upper()
        if "BO5" in format_text:
            return MatchFormat.BO5
        elif "BO3" in format_text:
            return MatchFormat.BO3
        else:
            return MatchFormat.BO1
    
    def _parse_datetime(self, date_str: str, time_str: str) -> Optional[datetime]:
        """Parse HLTV datetime format"""
        try:
            # HLTV uses various date formats, this is a simplified parser
            # In production, you'd need more robust date parsing
            combined = f"{date_str} {time_str}"
            return datetime.strptime(combined, "%Y-%m-%d %H:%M")
        except Exception as e:
            logger.warning(f"Failed to parse datetime: {date_str} {time_str} - {e}")
            return None
    
    async def get_upcoming_matches(self, target_date: datetime = None) -> List[Match]:
        """
        Scrape upcoming matches from HLTV
        
        Args:
            target_date: Date to filter matches for (default: today)
            
        Returns:
            List of Match objects
        """
        if target_date is None:
            target_date = datetime.now().date()
        
        logger.info(f"Fetching HLTV matches for {target_date}")
        
        matches_url = f"{self.base_url}/matches"
        html_content = self._make_flaresolverr_request(matches_url)
        
        if not html_content:
            logger.error("Failed to fetch HLTV matches page")
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        matches = []
        
        try:
            # Find match containers (this selector may need adjustment based on HLTV's current structure)
            match_containers = soup.find_all('div', class_='upcomingMatch')
            
            for container in match_containers:
                try:
                    match = self._parse_match_container(container, target_date)
                    if match:
                        matches.append(match)
                except Exception as e:
                    logger.warning(f"Failed to parse match container: {e}")
                    continue
            
            logger.info(f"Found {len(matches)} matches for {target_date}")
            
        except Exception as e:
            logger.error(f"Error parsing HLTV matches: {e}")
        
        return matches
    
    def _parse_match_container(self, container, target_date) -> Optional[Match]:
        """Parse individual match container from HLTV"""
        try:
            # Extract team names
            team_elements = container.find_all('div', class_='team')
            if len(team_elements) < 2:
                return None
            
            team1 = team_elements[0].find('div', class_='teamName').text.strip()
            team2 = team_elements[1].find('div', class_='teamName').text.strip()
            
            # Extract match time
            time_element = container.find('div', class_='matchTime')
            if not time_element:
                return None
            
            # Extract tournament
            event_element = container.find('div', class_='matchEvent')
            tournament = event_element.text.strip() if event_element else "Unknown"
            
            # Extract match format (BO1/BO3/BO5)
            format_element = container.find('div', class_='matchMeta')
            match_format = MatchFormat.BO1  # Default
            if format_element:
                format_text = format_element.text
                match_format = self._parse_match_format(format_text)
            
            # Create match ID
            match_id = f"hltv_{team1}_{team2}_{int(time.time())}"
            
            # For now, use current time + some offset as scheduled time
            # In production, you'd parse the actual time from HLTV
            scheduled_time = datetime.now() + timedelta(hours=1)
            
            match = Match(
                match_id=match_id,
                team1=team1,
                team2=team2,
                tournament=tournament,
                scheduled_time=scheduled_time,
                match_format=match_format,
                data_sources=["hltv_scraper"]
            )
            
            return match
            
        except Exception as e:
            logger.warning(f"Error parsing match container: {e}")
            return None
    
    async def get_team_stats(self, team_name: str, hltv_id: Optional[int] = None) -> Optional[TeamStats]:
        """
        Get detailed team statistics from HLTV
        
        Args:
            team_name: Name of the team
            hltv_id: HLTV team ID if known
            
        Returns:
            TeamStats object or None
        """
        logger.info(f"Fetching HLTV stats for team: {team_name}")
        
        # If we don't have HLTV ID, we'd need to search for it first
        if not hltv_id:
            hltv_id = await self._search_team_id(team_name)
            if not hltv_id:
                logger.warning(f"Could not find HLTV ID for team: {team_name}")
                return None
        
        team_url = f"{self.base_url}/team/{hltv_id}/{team_name.replace(' ', '-').lower()}"
        html_content = self._make_flaresolverr_request(team_url)
        
        if not html_content:
            logger.error(f"Failed to fetch team page for {team_name}")
            return None
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        try:
            team_stats = TeamStats(
                name=team_name,
                hltv_id=hltv_id,
                data_sources=["hltv_scraper"]
            )
            
            # Parse ranking
            ranking_element = soup.find('div', class_='profile-team-stat')
            if ranking_element:
                ranking_text = ranking_element.text
                # Extract ranking number from text like "#5"
                import re
                ranking_match = re.search(r'#(\d+)', ranking_text)
                if ranking_match:
                    team_stats.hltv_ranking = int(ranking_match.group(1))
            
            # Parse recent matches for win rate calculation
            matches_section = soup.find('div', class_='recent-results')
            if matches_section:
                recent_matches = self._parse_recent_matches(matches_section)
                team_stats.last_10_matches = recent_matches[:10]
                
                # Calculate win rate
                if recent_matches:
                    wins = sum(1 for match in recent_matches if match.get('result') == 'win')
                    team_stats.recent_win_rate = (wins / len(recent_matches)) * 100
            
            # Parse player roster
            roster_section = soup.find('div', class_='bodyshot-team')
            if roster_section:
                players = self._parse_team_roster(roster_section)
                team_stats.players = players
                
                # Calculate average rating
                ratings = [p.rating_2_0 for p in players if p.rating_2_0]
                if ratings:
                    team_stats.average_rating = sum(ratings) / len(ratings)
            
            logger.info(f"Successfully parsed stats for {team_name}")
            return team_stats
            
        except Exception as e:
            logger.error(f"Error parsing team stats for {team_name}: {e}")
            return None
    
    async def _search_team_id(self, team_name: str) -> Optional[int]:
        """Search for team HLTV ID by name"""
        # This would implement team search functionality
        # For now, return None to indicate ID not found
        logger.warning(f"Team ID search not implemented for: {team_name}")
        return None
    
    def _parse_recent_matches(self, matches_section) -> List[Dict[str, Any]]:
        """Parse recent matches from team page"""
        matches = []
        try:
            match_elements = matches_section.find_all('div', class_='result-con')
            for element in match_elements:
                # Parse match result
                result_element = element.find('span', class_='result-score')
                if result_element:
                    # Simplified parsing - in production you'd extract more details
                    matches.append({
                        'result': 'win' if 'won' in element.get('class', []) else 'loss',
                        'score': result_element.text.strip()
                    })
        except Exception as e:
            logger.warning(f"Error parsing recent matches: {e}")
        
        return matches
    
    def _parse_team_roster(self, roster_section) -> List[PlayerStats]:
        """Parse team roster from team page"""
        players = []
        try:
            player_elements = roster_section.find_all('div', class_='playerCol')
            for element in player_elements:
                name_element = element.find('div', class_='player-nick')
                if name_element:
                    player_name = name_element.text.strip()
                    
                    # Create basic player stats (in production, you'd fetch detailed stats)
                    player = PlayerStats(
                        name=player_name,
                        rating_2_0=1.0  # Placeholder - would fetch actual rating
                    )
                    players.append(player)
        except Exception as e:
            logger.warning(f"Error parsing team roster: {e}")
        
        return players
