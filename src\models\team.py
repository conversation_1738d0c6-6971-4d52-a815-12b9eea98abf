"""
Team and player data models for CS2 betting analysis
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class PlayerStats(BaseModel):
    """Individual player statistics"""
    name: str
    hltv_id: Optional[int] = None
    rating_2_0: Optional[float] = None
    adr: Optional[float] = None  # Average Damage per Round
    kast: Optional[float] = None  # Kill, Assist, Survive, Trade percentage
    impact: Optional[float] = None
    
    # Recent form (last 10 matches)
    recent_rating: Optional[float] = None
    recent_matches_played: Optional[int] = None
    
    # Map-specific performance
    map_ratings: Dict[str, float] = Field(default_factory=dict)
    
    # Role and position
    primary_role: Optional[str] = None  # IGL, AWPer, Entry, Support, Lurker
    
    class Config:
        use_enum_values = True


class TeamStats(BaseModel):
    """Team statistics and performance metrics"""
    name: str
    hltv_id: Optional[int] = None
    hltv_ranking: Optional[int] = None
    ranking_change: Optional[int] = None  # Change in last 30 days
    
    # Win rates
    overall_win_rate: Optional[float] = None
    lan_win_rate: Optional[float] = None
    online_win_rate: Optional[float] = None
    recent_win_rate: Optional[float] = None  # Last 10 matches
    
    # Team ratings
    average_rating: Optional[float] = None
    team_rating_2_0: Optional[float] = None
    
    # Map statistics
    map_win_rates: Dict[str, float] = Field(default_factory=dict)
    map_pick_rates: Dict[str, float] = Field(default_factory=dict)
    map_ban_rates: Dict[str, float] = Field(default_factory=dict)
    
    # Recent performance
    last_10_matches: List[Dict[str, Any]] = Field(default_factory=list)
    recent_form_trend: Optional[str] = None  # "improving", "declining", "stable"
    
    # Roster information
    players: List[PlayerStats] = Field(default_factory=list)
    recent_roster_changes: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Head-to-head records
    h2h_records: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    
    # Tournament-specific performance
    tournament_performance: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    
    # Data metadata
    last_updated: datetime = Field(default_factory=datetime.now)
    data_sources: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True


class TeamComparison(BaseModel):
    """Comparison between two teams"""
    team1: TeamStats
    team2: TeamStats
    
    # Comparative metrics
    ranking_gap: Optional[int] = None
    rating_difference: Optional[float] = None
    win_rate_difference: Optional[float] = None
    
    # Map pool analysis
    map_advantages: Dict[str, str] = Field(default_factory=dict)  # map -> favored_team
    map_pool_edge: Optional[str] = None  # Which team has overall map pool advantage
    
    # Head-to-head
    h2h_win_rate: Optional[float] = None  # team1 win rate vs team2
    h2h_matches_played: Optional[int] = None
    recent_h2h_trend: Optional[str] = None
    
    # Form comparison
    form_advantage: Optional[str] = None  # Which team is in better recent form
    
    # Key differentiators
    key_advantages: Dict[str, List[str]] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True


class AdvancedMetrics(BaseModel):
    """Advanced statistical metrics for team analysis"""
    team_name: str
    
    # Clutch performance
    clutch_win_rate: Optional[float] = None
    clutch_situations_per_map: Optional[float] = None
    
    # Economic performance
    eco_round_win_rate: Optional[float] = None
    force_buy_success_rate: Optional[float] = None
    save_round_efficiency: Optional[float] = None
    
    # Tactical metrics
    first_kill_percentage: Optional[float] = None
    trade_kill_percentage: Optional[float] = None
    retake_success_rate: Optional[float] = None
    
    # Map control
    t_side_win_rate: Optional[float] = None
    ct_side_win_rate: Optional[float] = None
    pistol_round_win_rate: Optional[float] = None
    
    # Consistency metrics
    performance_variance: Optional[float] = None
    upset_resistance: Optional[float] = None  # Performance vs higher-ranked teams
    
    # Psychological factors
    comeback_ability: Optional[float] = None  # Win rate when behind
    pressure_performance: Optional[float] = None  # Performance in important matches
    
    class Config:
        use_enum_values = True
