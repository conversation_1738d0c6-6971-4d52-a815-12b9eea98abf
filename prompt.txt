**CS2 Betting Analysis System: Automated Match Prediction Framework**

**Primary Objective:**
Build an automated system that identifies and analyzes the top 5 most profitable CS2 betting opportunities for the current day by:
1. Fetching upcoming matches from HLTV.org or bo3.gg
2. Using the hltv-stats library (https://github.com/researchersec/hltv-stats) to gather detailed team/player statistics ONLY for teams in upcoming matches
3. Implementing FlareSolverr (https://github.com/FlareSolverr/FlareSolverr) to bypass Cloudflare protection
4. Analyzing statistical data to identify high-probability betting opportunities with significant skill gaps

**Technical Requirements:**

1. **Match Discovery System:**
   - Primary: Scrape upcoming matches from https://www.hltv.org/matches
   - Fallback: Use https://bo3.gg/matches/current if HLTV fails
   - Filter matches to current day only
   - Extract team names, match format (BO1/BO3/BO5), and scheduled time

2. **Selective Data Fetching:**
   - Use hltv-stats library to fetch detailed statistics ONLY for teams appearing in today's matches
   - Avoid fetching all team data (too resource-intensive)
   - Required data points per team:
     - HLTV ranking and recent ranking changes
     - Win rate in last 10 matches (separate LAN/online)
     - Individual player ratings and recent form
     - Map-specific win rates and preferences
     - Head-to-head records against today's opponents

3. **Risk Assessment Algorithm:**
   Implement automated scoring based on:
   
   **Low Risk Bets (Target: 2-3 per day):**
   - HLTV ranking gap ≥ 10 positions
   - Favored team win rate ≥ 70% in last 10 matches
   - BO3/BO5 format only
   - Map pool advantage (≥ 65% win rate on 2+ maps where opponent has ≤ 50%)
   - No roster changes in last 30 days
   - Team average rating ≥ 1.10

   **Medium Risk Bets (Target: 2-3 per day):**
   - HLTV ranking gap ≥ 5 positions
   - Favored team win rate ≥ 60% in last 10 matches
   - BO3 preferred (BO1 with exceptional justification)
   - Map pool advantage on at least 1 map
   - Team average rating ≥ 1.05

4. **Output Format:**
   Generate ranked list of exactly 5 betting recommendations with:
   - Match details (teams, time, tournament)
   - Recommended bet type (moneyline, handicap, etc.)
   - Risk level (Low/Medium)
   - Confidence percentage (60-90%)
   - Key statistical justifications
   - Expected value calculation
   - Potential red flags

**Implementation Priorities:**
1. Set up FlareSolverr for Cloudflare bypass
2. Create match scraper for HLTV/bo3.gg
3. Integrate hltv-stats library with selective fetching
4. Build statistical analysis engine
5. Implement ranking algorithm
6. Create automated daily report generation

**Success Criteria:**
- System runs daily and identifies exactly 5 betting opportunities
- All recommendations include verifiable statistical backing
- Focus on "safe" bets where superior teams face significantly weaker opponents
- Minimize false positives by requiring multiple confirming indicators
- Generate actionable insights within 30 minutes of execution