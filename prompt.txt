**Project Title:** CS2 Betting Analysis: Ultimate Prediction Framework (Target Date: May 26, 2025)

**Overall Objective:**
Automate the setup and execution of a comprehensive CS2 betting analysis project. This project will identify, analyze, and rank the most promising CS2 match betting opportunities with the highest accuracy for matches scheduled on **May 26, 2025**. The system will prioritize value betting, use only verified data from authorized sources, and adhere strictly to the detailed criteria provided.

**Important Note on Target Date (May 26, 2025):**
This project is to be fully operational and perform its analysis for matches on May 26, 2025.
* During the current setup phase (prior to May 26, 2025), the focus is on building and testing the framework.
* You may use a recent past date (e.g., yesterday) or currently available upcoming matches as proxies for developing and testing data extraction, processing, and analysis logic. Clearly state when using proxy data.
* All final data fetching, odds, and analysis for recommendations **must** be performed using live data as of May 26, 2025.

**Phase 0: Environment Setup & Advanced Data Source Research/Selection**

1.  **Acknowledge Core Tools:**
    * The primary statistical analysis library initially considered is `researchersec/hltv-stats` (local installation).
    * FlareSolverr (`FlareSolverr/FlareSolverr`) is available for bypassing scraping challenges.

2.  **Mandatory Deep Search & Prioritization for Data Sources (ACTION):**
    * **Primary Goal:** Identify and prioritize the use of official or highly reliable APIs over manual scraping to ensure data accuracy, reduce maintenance, and enable selective data fetching.
    * **Investigate `GRID Open Access` (grid.gg/open-access/):**
        * Assess its suitability for fetching:
            * Upcoming CS2 matches specifically for May 26, 2025 (or proxy date).
            * Detailed team, player, and map statistics relevant ONLY to these upcoming matches.
        * Determine API access requirements, data granularity, and update frequency.
        * If viable, integrate as the primary data source.
    * **Contingency 1: Other High-Quality APIs:**
        * If GRID is unsuitable (e.g., access limitations, insufficient data depth for your detailed criteria), evaluate:
            * **PandaScore API:** Check free/developer tier capabilities for match schedules, detailed stats, and potentially odds.
            * **"CS2 Match Data API" (shmidtelson on RapidAPI):** Assess for upcoming matches and basic data.
        * Select the best available API based on data coverage, reliability, cost (prioritizing free/developer tiers), and ease of selective data fetching.
    * **Contingency 2: Advanced Scraping (If APIs are insufficient):**
        * If the above APIs do not meet the comprehensive data needs (especially the "Advanced Statistical Metrics" and very specific data points from HLTV), plan to use:
            * **HLTV Scrapers:**
                * Evaluate `akimerslys/hltv-async-api` as a potentially more modern/maintained alternative to `researchersec/hltv-stats`.
                * If `researchersec/hltv-stats` remains superior for specific deep historical stats needed for the upcoming matches, detail how you will use it **selectively**. This means if it downloads bulk data, you must immediately filter it to retain information *only* for the teams and players in the target upcoming matches.
                * Integrate FlareSolverr if direct scraping of HLTV.org is blocked.
            * **Bo3.gg Scraping:** As a supplementary source for upcoming matches (`bo3.gg/matches/current` or date-specific equivalent) and potentially cross-referencing stats.
    * **Decision & Documentation (ACTION):**
        * Clearly document which data source(s) you have selected for:
            1.  Upcoming match schedules.
            2.  Team/Player/Map statistics.
            3.  Betting odds (see below).
        * Provide a rationale for your choices, especially if deviating from `hltv-stats` or if combining multiple sources.

3.  **Odds Fetching (ACTION):**
    * Identify and integrate reliable source(s) for fetching decimal betting odds for the analyzed matches. Some esports APIs (e.g., PandaScore) may include this. If not, specify how odds will be obtained (e.g., scraping specific bookmaker sites, using an odds comparison API). Ensure the source URL can be cited as per user requirements.

4.  **Selective Data Strategy (CRITICAL):**
    * For ALL chosen data sources, implement a strategy to fetch or filter data **exclusively** for the teams and players participating in the identified upcoming matches for May 26, 2025. Avoid downloading or storing comprehensive historical data for unrelated entities.

5.  **Demo Analysis Integration (Recommended Enhancement - ACTION):**
    * Investigate methods to programmatically find and (if possible) download CS2 demo files for recent relevant matches (e.g., last 1-3 months) of teams scheduled to play on May 26, 2025. (HLTV match pages often list demos).
    * Integrate the `pnxenopoulos/awpy` library to parse these demos. This will be crucial for obtaining highly granular data for your "Advanced Statistical Metrics" and "Player Psychological Assessment" sections, supplementing API/scraped data.

6.  **Project Structure Setup (ACTION):**
    * Outline and create a directory structure for the project (e.g., `/data`, `/scripts`, `/analysis_results`, `/config`).
    * Install necessary Python libraries (e.g., `requests`, `beautifulsoup4`, `pandas`, `numpy`, the chosen HLTV library, `awpy`, clients for any selected APIs, `python-dotenv` for API keys).
    * Set up FlareSolverr if it's determined to be necessary.

**Phase 1: Data Acquisition (for May 26, 2025 Matches)**
*(To be run on May 26, 2025, or using proxy dates for development)*

1.  **Fetch Upcoming Matches (ACTION):**
    * Using the selected data source(s) from Phase 0, retrieve all CS2 matches scheduled for May 26, 2025 (or the proxy date).
    * Store essential match info: Match ID, teams, tournament, scheduled time (UTC).

2.  **Fetch Detailed Statistics (ACTION):**
    * For each identified upcoming match:
        * Using the selected data source(s) and the `awpy` integration (if demos are available):
            * Fetch/calculate all required team stats.
            * Fetch/calculate all required player stats.
            * Fetch/calculate all required map stats.
            * Fetch/calculate the "Advanced Statistical Metrics."
        * **Ensure data is specific to the context of the upcoming match and recent performance (e.g., last 3 months, last 5-10 matches as specified in criteria).**
        * Fetch current betting odds for the markets specified in "Bet Types to Analyze," citing the source.

3.  **Gather Supplementary Data (ACTION):**
    * For teams in promising matches, attempt to find:
        * Links to official team social media accounts (for checking last-minute roster changes).
        * Links to recent post-match interviews (if available, for qualitative team morale insights). This may require targeted web searches.

**Phase 2: Data Processing, Analysis & Filtering (Applying User Criteria)**
*(For each match fetched in Phase 1)*

1.  **Data Verification & Sufficiency (ACTION):**
    * Verify that all critical data points required by your risk assessment and advanced metrics are present.
    * Exclude matches where critical data is unavailable from all listed sources. Clearly label as "Data Insufficient."

2.  **Risk Assessment (ACTION):**
    * Apply the "Low Risk Criteria," "Medium Risk Criteria," and "High Risk" (automatic exclusion) rules.
    * For each match, document which risk criteria are met or not met. Only Low and Medium risk matches proceed.

3.  **Detailed Metric Analysis (ACTION):**
    * For all qualifying matches (Low/Medium Risk):
        * Calculate/assess all "Advanced Statistical Metrics."
        * Conduct "Player Psychological Assessment."
        * Conduct "Stand-in Impact Assessment" (if applicable).
        * Analyze "Map Pool Evolution."
        * Consider "Tournament-Specific Considerations."

4.  **Bet Type Analysis & Value Betting Calculation (ACTION):**
    * For each qualifying match, analyze the specified "Bet Types to Analyze" in order of priority.
    * For each potential bet:
        * Calculate implied probability: `(1 / decimal_odds) * 100`.
        * Determine your assessed probability based on all gathered data and analysis.
        * Calculate Expected Value (EV): `(Your_Assessed_Probability_Decimal * Decimal_Odds) - 1`.
        * **Only consider bets with clear positive EV.**

5.  **Confidence Rating (ACTION):**
    * Apply the "Confidence Rating Methodology" to each potential bet with positive EV.

6.  **Red Flag Identification & Odds Movement (ACTION):**
    * Identify any "Red Flags."
    * If possible (requires historical odds data or tracking), analyze "Odds Movement." If not possible, state "Odds movement data unavailable."

**Phase 3: Bet Selection, Ranking & Reporting**

1.  **Filter & Rank Bets (ACTION):**
    * Filter for bets meeting Low or Medium risk criteria AND having positive EV.
    * Rank these bets according to the "Ranking Methodology."

2.  **Select Top Recommendations (ACTION):**
    * Select the top 7 betting opportunities (or fewer if <7 qualify). Prioritize conservative selection.

3.  **Generate Output (ACTION):**
    * For EACH selected bet, generate the report in the specified "Output Format (For Each Bet)."
        * **Critical:** Every key data point MUST include a specific source citation (e.g., "GRID API, Team Stats Endpoint, May 26, 2025," "HLTV Team Profile via hltv-async-api, May 26, 2025," "awpy analysis of demo [demo_id], May 26, 2025," "Odds from [Bookmaker/Source URL], May 26, 2025").
        * Label "Odds unavailable," "H2H unavailable," or "Data insufficient" where applicable.

4.  **Compile Final Deliverable (ACTION):**
    * Create the "Final Deliverable" containing:
        * Ranked list of top 7 (or fewer) betting opportunities.
        * Brief explanation if fewer than 7 bets meet criteria.
        * Summary table: All recommended bets with risk level, confidence %, odds, and assessed probability/EV.
        * Alternative bets to consider if primary recommendations are unavailable.
        * Brief analysis of any betting market inefficiencies identified.

**Phase 4: Live Betting Considerations (Guidance Output)**

1.  **Generate Textual Guidance (ACTION):**
    * Based on the user's "Live Betting Considerations," provide a concise summary of strategies for potential in-play betting. This is for user reference, not for automated live betting by the agent.

**Critical Execution Requirements (To be adhered to throughout ALL phases):**
* **Data Verification:** All stats, odds, justifications attributable to listed sources as of May 26, 2025.
* **Citation Standard:** Every key data point sourced.
* **Target Date Focus:** Only matches for May 26, 2025 (or in progress if script runs through the day).
* **Data Sufficiency:** Exclude matches with critical missing data.
* **Value Betting Focus:** Only positive EV bets.
* **Risk Categorization:** Explicitly categorize each bet.
* **Ranking Methodology:** Follow strictly.
* **Conservative Selection:** Prioritize clear edge.
* **No Invented Data:** All data verifiable.
* **Comprehensive Analysis:** Analyze ALL scheduled matches before selection.
* **Confidence Rating Methodology:** Apply as defined.
* **Red Flag Identification:** Include this section.
* **Tournament-Specific Considerations:** Incorporate.
* **Odds Movement Analysis:** Attempt if data allows.
* **Transparency:** Clearly label missing data.

**Player & Team Assessment Guidelines (Guiding Principles for Analysis):**
* Consider roles, contribution, LAN vs. online, cohesion, meta adaptability, psychological factors, map vetos, coaching.

**Workflow Summary (Internal Agent Checklist):**
1.  Setup & Data Source Finalization (Phase 0)
2.  Gather Matches & Stats for Target Date (Phase 1)
3.  Filter, Analyze (Risk, Metrics, Value), Qualify Matches/Bets (Phase 2)
4.  Rank & Select Top Bets (Phase 3)
5.  Generate Detailed Output & Final Deliverable (Phase 3)
6.  Provide Live Betting Guidance (Phase 4)

**Interaction Protocol:**
* If you encounter ambiguity in requirements or face critical roadblocks (e.g., API access denial after attempting all contingencies, inability to fetch critical data for *any* match), clearly state the issue and ask for clarification or a decision on how to proceed.
* Provide progress updates at the end of each major phase.
* Request confirmation before proceeding with any step that might incur costs (if applicable outside of specified free tiers).

This enhanced prompt should give Gemini 2.5 Pro a clear, structured, and actionable plan to tackle this complex CS2 betting analysis project. Good luck!