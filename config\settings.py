"""
Configuration settings for CS2 Betting Analysis System
"""
import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings:
    """Application settings"""

    def __init__(self):
        # API Keys
        self.grid_api_key: Optional[str] = os.getenv("GRID_API_KEY")
        self.pandascore_api_key: Optional[str] = os.getenv("PANDASCORE_API_KEY")

        # FlareSolverr Configuration
        self.flaresolverr_url: str = os.getenv("FLARESOLVERR_URL", "http://localhost:8191/v1")

        # Logging Configuration
        self.log_level: str = os.getenv("LOG_LEVEL", "INFO")
        self.log_file: str = os.getenv("LOG_FILE", "logs/cs2_betting.log")

        # Analysis Configuration
        self.max_matches_per_day: int = int(os.getenv("MAX_MATCHES_PER_DAY", "20"))
        self.target_recommendations: int = int(os.getenv("TARGET_RECOMMENDATIONS", "5"))
        self.min_confidence_threshold: int = int(os.getenv("MIN_CONFIDENCE_THRESHOLD", "60"))

        # Data Source Priorities
        self.grid_api_priority: int = int(os.getenv("GRID_API_PRIORITY", "1"))
        self.pandascore_api_priority: int = int(os.getenv("PANDASCORE_API_PRIORITY", "2"))
        self.hltv_scraping_priority: int = int(os.getenv("HLTV_SCRAPING_PRIORITY", "3"))
        self.bo3_scraping_priority: int = int(os.getenv("BO3_SCRAPING_PRIORITY", "4"))

        # Risk Assessment Thresholds
        self.low_risk_ranking_gap: int = int(os.getenv("LOW_RISK_RANKING_GAP", "10"))
        self.medium_risk_ranking_gap: int = int(os.getenv("MEDIUM_RISK_RANKING_GAP", "5"))
        self.low_risk_win_rate: int = int(os.getenv("LOW_RISK_WIN_RATE", "70"))
        self.medium_risk_win_rate: int = int(os.getenv("MEDIUM_RISK_WIN_RATE", "60"))
        self.min_team_rating: float = float(os.getenv("MIN_TEAM_RATING", "1.05"))

        # Timeouts and Delays
        self.request_timeout: int = int(os.getenv("REQUEST_TIMEOUT", "30"))
        self.rate_limit_delay: int = int(os.getenv("RATE_LIMIT_DELAY", "1"))
        self.max_retries: int = int(os.getenv("MAX_RETRIES", "3"))

        # URLs
        self.hltv_matches_url: str = "https://www.hltv.org/matches"
        self.bo3_matches_url: str = "https://bo3.gg/matches/current"
        self.grid_api_base_url: str = "https://api.grid.gg"
        self.pandascore_api_base_url: str = "https://api.pandascore.co"


# Global settings instance
settings = Settings()


def get_data_source_priority() -> list:
    """Get data sources ordered by priority"""
    sources = [
        (settings.grid_api_priority, "grid_api"),
        (settings.pandascore_api_priority, "pandascore_api"),
        (settings.hltv_scraping_priority, "hltv_scraping"),
        (settings.bo3_scraping_priority, "bo3_scraping")
    ]
    return [source[1] for source in sorted(sources, key=lambda x: x[0])]


def validate_api_keys() -> dict:
    """Validate which API keys are available"""
    return {
        "grid_api": bool(settings.grid_api_key),
        "pandascore_api": bool(settings.pandascore_api_key)
    }
