"""
Demo script for CS2 Betting Analysis System
Shows basic functionality with mock data
"""
import asyncio
from datetime import datetime, timedelta
from src.models.match import Match, MatchFormat, MatchPrediction, BetType, RiskLevel
from src.models.team import TeamStats, PlayerStats, TeamComparison
from src.analysis.risk_assessment import RiskAssessment
from src.utils.data_validator import DataValidator
from src.utils.logger import get_logger

logger = get_logger("Demo")


def create_mock_data():
    """Create mock data for demonstration"""

    # Create mock matches
    matches = [
        Match(
            match_id="demo_match_1",
            team1="Team Liquid",
            team2="FURIA",
            tournament="IEM Katowice 2025",
            scheduled_time=datetime.now() + timedelta(hours=2),
            match_format=MatchFormat.BO3,
            data_sources=["demo"]
        ),
        Match(
            match_id="demo_match_2",
            team1="NAVI",
            team2="Complexity",
            tournament="BLAST Premier Spring",
            scheduled_time=datetime.now() + timedelta(hours=4),
            match_format=MatchFormat.BO3,
            data_sources=["demo"]
        ),
        Match(
            match_id="demo_match_3",
            team1="Astralis",
            team2="Heroic",
            tournament="ESL Pro League",
            scheduled_time=datetime.now() + timedelta(hours=6),
            match_format=MatchFormat.BO1,
            data_sources=["demo"]
        )
    ]

    # Create mock team statistics
    team_stats = {
        "Team Liquid": TeamStats(
            name="Team Liquid",
            hltv_ranking=3,
            overall_win_rate=72.5,
            recent_win_rate=80.0,
            average_rating=1.12,
            map_win_rates={
                "dust2": 75.0,
                "mirage": 82.0,
                "inferno": 68.0,
                "overpass": 71.0,
                "vertigo": 65.0
            },
            players=[
                PlayerStats(name="YEKINDAR", rating_2_0=1.15),
                PlayerStats(name="NAF", rating_2_0=1.08),
                PlayerStats(name="oSee", rating_2_0=1.10),
                PlayerStats(name="ULTIMATE", rating_2_0=1.05),
                PlayerStats(name="cadiaN", rating_2_0=1.02)
            ],
            data_sources=["demo"]
        ),
        "FURIA": TeamStats(
            name="FURIA",
            hltv_ranking=15,
            overall_win_rate=58.3,
            recent_win_rate=55.0,
            average_rating=1.03,
            map_win_rates={
                "dust2": 45.0,
                "mirage": 62.0,
                "inferno": 48.0,
                "overpass": 52.0,
                "vertigo": 58.0
            },
            players=[
                PlayerStats(name="yuurih", rating_2_0=1.08),
                PlayerStats(name="KSCERATO", rating_2_0=1.05),
                PlayerStats(name="arT", rating_2_0=0.98),
                PlayerStats(name="drop", rating_2_0=1.02),
                PlayerStats(name="skullz", rating_2_0=1.01)
            ],
            data_sources=["demo"]
        ),
        "NAVI": TeamStats(
            name="NAVI",
            hltv_ranking=1,
            overall_win_rate=78.9,
            recent_win_rate=85.0,
            average_rating=1.18,
            map_win_rates={
                "dust2": 82.0,
                "mirage": 79.0,
                "inferno": 85.0,
                "overpass": 76.0,
                "vertigo": 71.0
            },
            players=[
                PlayerStats(name="s1mple", rating_2_0=1.25),
                PlayerStats(name="electronic", rating_2_0=1.15),
                PlayerStats(name="Perfecto", rating_2_0=1.08),
                PlayerStats(name="b1t", rating_2_0=1.12),
                PlayerStats(name="Boombl4", rating_2_0=1.05)
            ],
            data_sources=["demo"]
        ),
        "Complexity": TeamStats(
            name="Complexity",
            hltv_ranking=8,
            overall_win_rate=65.2,
            recent_win_rate=60.0,
            average_rating=1.06,
            map_win_rates={
                "dust2": 58.0,
                "mirage": 68.0,
                "inferno": 62.0,
                "overpass": 65.0,
                "vertigo": 55.0
            },
            players=[
                PlayerStats(name="EliGE", rating_2_0=1.12),
                PlayerStats(name="Grim", rating_2_0=1.08),
                PlayerStats(name="floppy", rating_2_0=1.02),
                PlayerStats(name="JT", rating_2_0=1.05),
                PlayerStats(name="FaNg", rating_2_0=0.98)
            ],
            recent_roster_changes=[
                {"date": datetime.now() - timedelta(days=15), "type": "addition", "player": "FaNg"}
            ],
            data_sources=["demo"]
        ),
        "Astralis": TeamStats(
            name="Astralis",
            hltv_ranking=12,
            overall_win_rate=61.8,
            recent_win_rate=58.0,
            average_rating=1.04,
            map_win_rates={
                "dust2": 55.0,
                "mirage": 64.0,
                "inferno": 59.0,
                "overpass": 62.0,
                "vertigo": 48.0
            },
            data_sources=["demo"]
        ),
        "Heroic": TeamStats(
            name="Heroic",
            hltv_ranking=18,
            overall_win_rate=54.7,
            recent_win_rate=52.0,
            average_rating=1.01,
            map_win_rates={
                "dust2": 48.0,
                "mirage": 56.0,
                "inferno": 51.0,
                "overpass": 54.0,
                "vertigo": 49.0
            },
            data_sources=["demo"]
        )
    }

    return matches, team_stats


def create_team_comparison(team1_stats: TeamStats, team2_stats: TeamStats) -> TeamComparison:
    """Create team comparison object"""
    ranking_gap = None
    if team1_stats.hltv_ranking and team2_stats.hltv_ranking:
        ranking_gap = abs(team1_stats.hltv_ranking - team2_stats.hltv_ranking)

    win_rate_diff = None
    if team1_stats.recent_win_rate and team2_stats.recent_win_rate:
        win_rate_diff = abs(team1_stats.recent_win_rate - team2_stats.recent_win_rate)

    return TeamComparison(
        team1=team1_stats,
        team2=team2_stats,
        ranking_gap=ranking_gap,
        win_rate_difference=win_rate_diff
    )


async def run_demo():
    """Run the demo analysis"""
    print("CS2 Betting Analysis System - Demo")
    print("=" * 50)

    # Create mock data
    matches, team_stats = create_mock_data()

    print(f"Created {len(matches)} demo matches")
    print(f"Created stats for {len(team_stats)} teams")

    # Initialize components
    risk_assessor = RiskAssessment()
    validator = DataValidator()

    print("\nValidating data quality...")

    # Validate data
    sufficiency_report = validator.get_data_sufficiency_report(matches, team_stats)
    print(f"Data quality score: {sufficiency_report['overall_data_quality']:.2f}")

    predictions = []

    print("\nAnalyzing matches...")

    # Analyze each match
    for match in matches:
        print(f"\nAnalyzing: {match.team1} vs {match.team2}")

        team1_stats = team_stats.get(match.team1)
        team2_stats = team_stats.get(match.team2)

        if not team1_stats or not team2_stats:
            print(f"  ⚠ Missing team stats - skipping")
            continue

        # Create team comparison
        team_comparison = create_team_comparison(team1_stats, team2_stats)

        # Assess risk
        risk_level, assessment_details, red_flags = risk_assessor.assess_match_risk(
            match, team1_stats, team2_stats, team_comparison
        )

        print(f"  Risk Level: {risk_level.value}")
        print(f"  Ranking Gap: {team_comparison.ranking_gap}")
        print(f"  Win Rate Difference: {team_comparison.win_rate_difference:.1f}%")

        if red_flags:
            print(f"  Red Flags: {', '.join(red_flags)}")

        # Skip high-risk matches
        if risk_level == RiskLevel.HIGH:
            print(f"  ❌ Excluded (High Risk)")
            continue

        # Create prediction
        favored_team = assessment_details.get('favored_team', match.team1)
        confidence = 75 if risk_level == RiskLevel.LOW else 68
        expected_value = 0.15 if risk_level == RiskLevel.LOW else 0.08

        prediction = MatchPrediction(
            match=match,
            recommended_bet_type=BetType.MONEYLINE,
            recommended_team=favored_team,
            confidence_percentage=confidence,
            expected_value=expected_value,
            risk_level=risk_level,
            ranking_gap=team_comparison.ranking_gap,
            win_rate_difference=team_comparison.win_rate_difference,
            red_flags=red_flags,
            key_stats=assessment_details,
            data_citations=[f"Demo analysis - {datetime.now().isoformat()}"]
        )

        predictions.append(prediction)
        print(f"  ✅ Added to recommendations")

    # Sort predictions by expected value
    predictions.sort(key=lambda p: (p.expected_value, p.confidence_percentage), reverse=True)

    print("\n" + "=" * 60)
    print("BETTING RECOMMENDATIONS")
    print("=" * 60)

    if predictions:
        for i, pred in enumerate(predictions[:5], 1):
            print(f"\n{i}. {pred.match.team1} vs {pred.match.team2}")
            print(f"   Tournament: {pred.match.tournament}")
            print(f"   Format: {pred.match.match_format}")
            print(f"   Scheduled: {pred.match.scheduled_time.strftime('%H:%M')}")
            print(f"   Recommended Bet: {pred.recommended_bet_type} on {pred.recommended_team}")
            print(f"   Risk Level: {pred.risk_level}")
            print(f"   Confidence: {pred.confidence_percentage:.1f}%")
            print(f"   Expected Value: {pred.expected_value:.3f}")

            if pred.ranking_gap:
                print(f"   Ranking Gap: {pred.ranking_gap} positions")
            if pred.win_rate_difference:
                print(f"   Win Rate Difference: {pred.win_rate_difference:.1f}%")

            if pred.red_flags:
                print(f"   ⚠ Red Flags: {', '.join(pred.red_flags)}")
    else:
        print("No betting recommendations generated.")

    print(f"\nDemo completed! Found {len(predictions)} qualifying opportunities.")


if __name__ == "__main__":
    asyncio.run(run_demo())
