#!/usr/bin/env python3
"""
Test script for HLTV scraper
"""
import asyncio
from datetime import datetime
from src.scrapers.hltv_scraper import HLTVScraper

async def test_hltv():
    print('Initializing HLTV scraper...')
    scraper = HLTVScraper()
    target_date = datetime(2025, 5, 26)

    date_str = target_date.strftime("%Y-%m-%d")
    print(f'Testing HLTV scraper for {date_str}...')
    print(f'Using FlareSolverr at: http://localhost:8191/v1')

    try:
        print('Calling get_upcoming_matches...')
        matches = await scraper.get_upcoming_matches(target_date)
        print(f'Found {len(matches)} matches:')

        for i, match in enumerate(matches[:10], 1):  # Show first 10 matches
            time_str = match.scheduled_time.strftime("%H:%M")
            print(f'{i}. {match.team1} vs {match.team2} at {time_str} - {match.tournament}')

    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_hltv())
