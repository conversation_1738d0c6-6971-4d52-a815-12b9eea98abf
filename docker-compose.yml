version: '3.8'

services:
  flaresolverr:
    image: ghcr.io/flaresolverr/flaresolverr:latest
    container_name: flaresolverr
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_HTML=${LOG_HTML:-false}
      - CAPTCHA_SOLVER=${CAPTCHA_SOLVER:-none}
    ports:
      - "8191:8191"
    restart: unless-stopped
    networks:
      - cs2-betting-network

networks:
  cs2-betting-network:
    driver: bridge
