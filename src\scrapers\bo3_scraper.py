"""
Bo3.gg scraper for CS2 match data (fallback source)
"""
import asyncio
import aiohttp
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from bs4 import BeautifulSoup
import time

from src.models.match import Match, MatchFormat
from src.utils.logger import get_logger
from config.settings import settings

logger = get_logger("Bo3Scraper")


class Bo3Scraper:
    """Scraper for Bo3.gg match data"""
    
    def __init__(self):
        self.base_url = "https://bo3.gg"
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=settings.request_timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _make_request(self, url: str) -> Optional[str]:
        """Make HTTP request with retry logic"""
        for attempt in range(settings.max_retries):
            try:
                response = requests.get(
                    url,
                    timeout=settings.request_timeout,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                )
                
                if response.status_code == 200:
                    return response.text
                else:
                    logger.warning(f"HTTP {response.status_code} for {url}")
                    
            except Exception as e:
                logger.warning(f"Request attempt {attempt + 1} failed for {url}: {e}")
                if attempt < settings.max_retries - 1:
                    time.sleep(settings.rate_limit_delay * (attempt + 1))
        
        return None
    
    def _parse_match_format(self, format_text: str) -> MatchFormat:
        """Parse match format from text"""
        format_text = format_text.upper()
        if "BO5" in format_text or "BEST OF 5" in format_text:
            return MatchFormat.BO5
        elif "BO3" in format_text or "BEST OF 3" in format_text:
            return MatchFormat.BO3
        else:
            return MatchFormat.BO1
    
    async def get_upcoming_matches(self, target_date: datetime = None) -> List[Match]:
        """
        Scrape upcoming matches from Bo3.gg
        
        Args:
            target_date: Date to filter matches for (default: today)
            
        Returns:
            List of Match objects
        """
        if target_date is None:
            target_date = datetime.now().date()
        
        logger.info(f"Fetching Bo3.gg matches for {target_date}")
        
        matches_url = f"{self.base_url}/matches/current"
        html_content = self._make_request(matches_url)
        
        if not html_content:
            logger.error("Failed to fetch Bo3.gg matches page")
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        matches = []
        
        try:
            # Find match containers (selectors may need adjustment based on Bo3.gg structure)
            match_containers = soup.find_all('div', class_='match-card') or soup.find_all('div', class_='match-item')
            
            for container in match_containers:
                try:
                    match = self._parse_match_container(container, target_date)
                    if match:
                        matches.append(match)
                except Exception as e:
                    logger.warning(f"Failed to parse match container: {e}")
                    continue
            
            logger.info(f"Found {len(matches)} matches from Bo3.gg for {target_date}")
            
        except Exception as e:
            logger.error(f"Error parsing Bo3.gg matches: {e}")
        
        return matches
    
    def _parse_match_container(self, container, target_date) -> Optional[Match]:
        """Parse individual match container from Bo3.gg"""
        try:
            # Extract team names - Bo3.gg structure may vary
            team_elements = container.find_all('div', class_='team-name') or container.find_all('span', class_='team')
            
            if len(team_elements) < 2:
                # Try alternative selectors
                team_links = container.find_all('a', href=lambda x: x and '/team/' in x)
                if len(team_links) >= 2:
                    team1 = team_links[0].text.strip()
                    team2 = team_links[1].text.strip()
                else:
                    return None
            else:
                team1 = team_elements[0].text.strip()
                team2 = team_elements[1].text.strip()
            
            # Extract match time
            time_element = container.find('div', class_='match-time') or container.find('span', class_='time')
            scheduled_time = datetime.now() + timedelta(hours=2)  # Default fallback
            
            if time_element:
                time_text = time_element.text.strip()
                # Parse time if possible (Bo3.gg format may vary)
                scheduled_time = self._parse_bo3_time(time_text)
            
            # Extract tournament
            tournament_element = container.find('div', class_='tournament') or container.find('span', class_='event')
            tournament = tournament_element.text.strip() if tournament_element else "Unknown Tournament"
            
            # Extract match format
            format_element = container.find('div', class_='format') or container.find('span', class_='bo')
            match_format = MatchFormat.BO1  # Default
            
            if format_element:
                format_text = format_element.text
                match_format = self._parse_match_format(format_text)
            
            # Create match ID
            match_id = f"bo3_{team1}_{team2}_{int(time.time())}"
            
            match = Match(
                match_id=match_id,
                team1=team1,
                team2=team2,
                tournament=tournament,
                scheduled_time=scheduled_time,
                match_format=match_format,
                data_sources=["bo3_scraper"]
            )
            
            return match
            
        except Exception as e:
            logger.warning(f"Error parsing Bo3.gg match container: {e}")
            return None
    
    def _parse_bo3_time(self, time_text: str) -> datetime:
        """Parse time from Bo3.gg format"""
        try:
            # Bo3.gg may use various time formats
            # This is a simplified parser - in production you'd handle multiple formats
            
            # Handle "in X hours" format
            if "in" in time_text.lower() and "hour" in time_text.lower():
                import re
                hours_match = re.search(r'(\d+)\s*hour', time_text)
                if hours_match:
                    hours = int(hours_match.group(1))
                    return datetime.now() + timedelta(hours=hours)
            
            # Handle "in X minutes" format
            if "in" in time_text.lower() and "minute" in time_text.lower():
                import re
                minutes_match = re.search(r'(\d+)\s*minute', time_text)
                if minutes_match:
                    minutes = int(minutes_match.group(1))
                    return datetime.now() + timedelta(minutes=minutes)
            
            # Handle specific time format like "14:30"
            if ":" in time_text:
                try:
                    time_part = time_text.split()[0]  # Get first part if there are multiple words
                    hour, minute = map(int, time_part.split(':'))
                    
                    # Assume it's today if time is in the future, tomorrow if in the past
                    now = datetime.now()
                    match_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                    
                    if match_time < now:
                        match_time += timedelta(days=1)
                    
                    return match_time
                except:
                    pass
            
        except Exception as e:
            logger.warning(f"Failed to parse Bo3.gg time '{time_text}': {e}")
        
        # Fallback to 2 hours from now
        return datetime.now() + timedelta(hours=2)
    
    async def get_match_details(self, match_url: str) -> Dict[str, Any]:
        """
        Get additional match details from Bo3.gg match page
        
        Args:
            match_url: URL to the specific match page
            
        Returns:
            Dictionary with additional match details
        """
        html_content = self._make_request(match_url)
        
        if not html_content:
            logger.error(f"Failed to fetch match details from {match_url}")
            return {}
        
        soup = BeautifulSoup(html_content, 'html.parser')
        details = {}
        
        try:
            # Extract odds if available
            odds_section = soup.find('div', class_='odds') or soup.find('section', class_='betting')
            if odds_section:
                details['odds'] = self._parse_odds_section(odds_section)
            
            # Extract map pool if available
            maps_section = soup.find('div', class_='maps') or soup.find('section', class_='mappool')
            if maps_section:
                details['maps'] = self._parse_maps_section(maps_section)
            
            # Extract additional tournament info
            tournament_section = soup.find('div', class_='tournament-info')
            if tournament_section:
                details['tournament_details'] = self._parse_tournament_section(tournament_section)
            
        except Exception as e:
            logger.warning(f"Error parsing match details: {e}")
        
        return details
    
    def _parse_odds_section(self, odds_section) -> Dict[str, float]:
        """Parse betting odds from Bo3.gg"""
        odds = {}
        try:
            # Look for odds elements (structure may vary)
            odds_elements = odds_section.find_all('span', class_='odd') or odds_section.find_all('div', class_='odds-value')
            
            if len(odds_elements) >= 2:
                odds['team1_moneyline'] = float(odds_elements[0].text.strip())
                odds['team2_moneyline'] = float(odds_elements[1].text.strip())
                
        except Exception as e:
            logger.warning(f"Error parsing odds: {e}")
        
        return odds
    
    def _parse_maps_section(self, maps_section) -> List[str]:
        """Parse map pool from Bo3.gg"""
        maps = []
        try:
            map_elements = maps_section.find_all('span', class_='map') or maps_section.find_all('div', class_='map-name')
            
            for element in map_elements:
                map_name = element.text.strip()
                if map_name:
                    maps.append(map_name)
                    
        except Exception as e:
            logger.warning(f"Error parsing maps: {e}")
        
        return maps
    
    def _parse_tournament_section(self, tournament_section) -> Dict[str, str]:
        """Parse tournament details from Bo3.gg"""
        details = {}
        try:
            # Extract tournament tier, prize pool, etc.
            tier_element = tournament_section.find('span', class_='tier')
            if tier_element:
                details['tier'] = tier_element.text.strip()
            
            prize_element = tournament_section.find('span', class_='prize')
            if prize_element:
                details['prize_pool'] = prize_element.text.strip()
                
        except Exception as e:
            logger.warning(f"Error parsing tournament details: {e}")
        
        return details
