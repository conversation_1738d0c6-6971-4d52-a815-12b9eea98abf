"""
Match data models for CS2 betting analysis
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class MatchFormat(str, Enum):
    """Match format types"""
    BO1 = "BO1"
    BO3 = "BO3"
    BO5 = "BO5"


class RiskLevel(str, Enum):
    """Risk assessment levels"""
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"


class BetType(str, Enum):
    """Available bet types"""
    MONEYLINE = "Moneyline"
    HANDICAP = "Handicap"
    OVER_UNDER = "Over/Under"
    MAP_WINNER = "Map Winner"
    FIRST_MAP = "First Map"


class MatchOdds(BaseModel):
    """Betting odds for a match"""
    team1_moneyline: Optional[float] = None
    team2_moneyline: Optional[float] = None
    team1_handicap: Optional[Dict[str, float]] = None  # e.g., {"-1.5": 2.1, "+1.5": 1.8}
    team2_handicap: Optional[Dict[str, float]] = None
    over_under_maps: Optional[Dict[str, float]] = None  # e.g., {"2.5": {"over": 1.9, "under": 1.9}}
    source: Optional[str] = None
    last_updated: Optional[datetime] = None


class Match(BaseModel):
    """CS2 match data model"""
    match_id: str
    team1: str
    team2: str
    tournament: str
    scheduled_time: datetime
    match_format: MatchFormat
    
    # Optional fields that may be populated during analysis
    team1_hltv_id: Optional[int] = None
    team2_hltv_id: Optional[int] = None
    map_pool: Optional[List[str]] = None
    odds: Optional[MatchOdds] = None
    
    # Analysis results
    risk_level: Optional[RiskLevel] = None
    confidence_score: Optional[float] = None
    expected_value: Optional[float] = None
    recommended_bet: Optional[BetType] = None
    
    # Data sources
    data_sources: List[str] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=datetime.now)
    
    class Config:
        use_enum_values = True


class MatchPrediction(BaseModel):
    """Betting prediction for a match"""
    match: Match
    recommended_bet_type: BetType
    recommended_team: str
    confidence_percentage: float
    expected_value: float
    risk_level: RiskLevel
    
    # Statistical justifications
    ranking_gap: Optional[int] = None
    win_rate_difference: Optional[float] = None
    map_pool_advantage: Optional[str] = None
    recent_form_analysis: Optional[str] = None
    
    # Red flags
    red_flags: List[str] = Field(default_factory=list)
    
    # Key statistics
    key_stats: Dict[str, Any] = Field(default_factory=dict)
    
    # Data citations
    data_citations: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True


class DailyReport(BaseModel):
    """Daily betting analysis report"""
    date: datetime
    total_matches_analyzed: int
    recommendations: List[MatchPrediction]
    alternative_bets: List[MatchPrediction] = Field(default_factory=list)
    
    # Summary statistics
    avg_confidence: float
    avg_expected_value: float
    risk_distribution: Dict[str, int] = Field(default_factory=dict)
    
    # Market analysis
    market_inefficiencies: List[str] = Field(default_factory=list)
    
    # Execution metadata
    execution_time_seconds: Optional[float] = None
    data_sources_used: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True
