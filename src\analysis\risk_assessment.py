"""
Risk assessment module for CS2 betting analysis
"""
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from src.models.match import Match, MatchFormat, RiskLevel
from src.models.team import TeamStats, TeamComparison
from src.utils.logger import get_logger
from config.settings import settings

logger = get_logger("RiskAssessment")


class RiskAssessment:
    """Risk assessment engine for CS2 betting opportunities"""
    
    def __init__(self):
        self.low_risk_criteria = {
            'ranking_gap': settings.low_risk_ranking_gap,
            'win_rate': settings.low_risk_win_rate,
            'min_rating': settings.min_team_rating,
            'required_formats': [MatchFormat.BO3, MatchFormat.BO5],
            'map_advantage_threshold': 65,
            'map_advantage_count': 2
        }
        
        self.medium_risk_criteria = {
            'ranking_gap': settings.medium_risk_ranking_gap,
            'win_rate': settings.medium_risk_win_rate,
            'min_rating': settings.min_team_rating,
            'preferred_formats': [MatchFormat.BO3, MatchFormat.BO5],
            'map_advantage_threshold': 60,
            'map_advantage_count': 1
        }
    
    def assess_match_risk(
        self, 
        match: Match, 
        team1_stats: TeamStats, 
        team2_stats: TeamStats,
        team_comparison: TeamComparison
    ) -> Tuple[RiskLevel, Dict[str, any], List[str]]:
        """
        Assess the risk level of a betting opportunity
        
        Args:
            match: Match object
            team1_stats: Statistics for team 1
            team2_stats: Statistics for team 2
            team_comparison: Comparison between teams
            
        Returns:
            Tuple of (risk_level, assessment_details, red_flags)
        """
        logger.info(f"Assessing risk for match: {match.team1} vs {match.team2}")
        
        assessment_details = {}
        red_flags = []
        
        # Determine favored team based on ranking
        if team1_stats.hltv_ranking and team2_stats.hltv_ranking:
            if team1_stats.hltv_ranking < team2_stats.hltv_ranking:
                favored_team = team1_stats
                underdog_team = team2_stats
                favored_name = match.team1
            else:
                favored_team = team2_stats
                underdog_team = team1_stats
                favored_name = match.team2
        else:
            # Fallback to rating comparison
            team1_rating = team1_stats.average_rating or 0
            team2_rating = team2_stats.average_rating or 0
            
            if team1_rating > team2_rating:
                favored_team = team1_stats
                underdog_team = team2_stats
                favored_name = match.team1
            else:
                favored_team = team2_stats
                underdog_team = team1_stats
                favored_name = match.team2
        
        assessment_details['favored_team'] = favored_name
        
        # Check for low risk criteria
        low_risk_score = self._evaluate_low_risk_criteria(
            match, favored_team, underdog_team, team_comparison, assessment_details, red_flags
        )
        
        if low_risk_score >= 0.8:
            logger.info(f"Match classified as LOW RISK (score: {low_risk_score:.2f})")
            return RiskLevel.LOW, assessment_details, red_flags
        
        # Check for medium risk criteria
        medium_risk_score = self._evaluate_medium_risk_criteria(
            match, favored_team, underdog_team, team_comparison, assessment_details, red_flags
        )
        
        if medium_risk_score >= 0.6:
            logger.info(f"Match classified as MEDIUM RISK (score: {medium_risk_score:.2f})")
            return RiskLevel.MEDIUM, assessment_details, red_flags
        
        # High risk (excluded)
        logger.info(f"Match classified as HIGH RISK - excluding from recommendations")
        return RiskLevel.HIGH, assessment_details, red_flags
    
    def _evaluate_low_risk_criteria(
        self, 
        match: Match, 
        favored_team: TeamStats, 
        underdog_team: TeamStats,
        team_comparison: TeamComparison,
        assessment_details: Dict,
        red_flags: List[str]
    ) -> float:
        """Evaluate low risk criteria and return score (0-1)"""
        
        criteria_scores = []
        
        # 1. HLTV ranking gap ≥ 10 positions
        ranking_gap = team_comparison.ranking_gap or 0
        assessment_details['ranking_gap'] = ranking_gap
        
        if ranking_gap >= self.low_risk_criteria['ranking_gap']:
            criteria_scores.append(1.0)
        else:
            criteria_scores.append(ranking_gap / self.low_risk_criteria['ranking_gap'])
            if ranking_gap < 5:
                red_flags.append(f"Small ranking gap: {ranking_gap} positions")
        
        # 2. Favored team win rate ≥ 70% in last 10 matches
        win_rate = favored_team.recent_win_rate or 0
        assessment_details['favored_team_win_rate'] = win_rate
        
        if win_rate >= self.low_risk_criteria['win_rate']:
            criteria_scores.append(1.0)
        else:
            criteria_scores.append(win_rate / self.low_risk_criteria['win_rate'])
            if win_rate < 60:
                red_flags.append(f"Low recent win rate: {win_rate:.1f}%")
        
        # 3. BO3/BO5 format only
        if match.match_format in self.low_risk_criteria['required_formats']:
            criteria_scores.append(1.0)
            assessment_details['format_acceptable'] = True
        else:
            criteria_scores.append(0.0)
            assessment_details['format_acceptable'] = False
            red_flags.append(f"Risky format: {match.match_format}")
        
        # 4. Map pool advantage (≥ 65% win rate on 2+ maps where opponent has ≤ 50%)
        map_advantage_score = self._evaluate_map_pool_advantage(
            favored_team, underdog_team, 
            self.low_risk_criteria['map_advantage_threshold'],
            self.low_risk_criteria['map_advantage_count']
        )
        criteria_scores.append(map_advantage_score)
        assessment_details['map_pool_advantage_score'] = map_advantage_score
        
        if map_advantage_score < 0.5:
            red_flags.append("Insufficient map pool advantage")
        
        # 5. No roster changes in last 30 days
        roster_stability_score = self._evaluate_roster_stability(favored_team, underdog_team)
        criteria_scores.append(roster_stability_score)
        assessment_details['roster_stability_score'] = roster_stability_score
        
        if roster_stability_score < 0.8:
            red_flags.append("Recent roster changes detected")
        
        # 6. Team average rating ≥ 1.10
        avg_rating = favored_team.average_rating or 0
        assessment_details['favored_team_rating'] = avg_rating
        
        if avg_rating >= 1.10:
            criteria_scores.append(1.0)
        else:
            criteria_scores.append(max(0, (avg_rating - 1.0) / 0.1))
            if avg_rating < 1.05:
                red_flags.append(f"Low team rating: {avg_rating:.2f}")
        
        return sum(criteria_scores) / len(criteria_scores)
    
    def _evaluate_medium_risk_criteria(
        self, 
        match: Match, 
        favored_team: TeamStats, 
        underdog_team: TeamStats,
        team_comparison: TeamComparison,
        assessment_details: Dict,
        red_flags: List[str]
    ) -> float:
        """Evaluate medium risk criteria and return score (0-1)"""
        
        criteria_scores = []
        
        # 1. HLTV ranking gap ≥ 5 positions
        ranking_gap = team_comparison.ranking_gap or 0
        
        if ranking_gap >= self.medium_risk_criteria['ranking_gap']:
            criteria_scores.append(1.0)
        else:
            criteria_scores.append(ranking_gap / self.medium_risk_criteria['ranking_gap'])
        
        # 2. Favored team win rate ≥ 60% in last 10 matches
        win_rate = favored_team.recent_win_rate or 0
        
        if win_rate >= self.medium_risk_criteria['win_rate']:
            criteria_scores.append(1.0)
        else:
            criteria_scores.append(win_rate / self.medium_risk_criteria['win_rate'])
        
        # 3. BO3 preferred (BO1 with exceptional justification)
        if match.match_format == MatchFormat.BO3:
            criteria_scores.append(1.0)
        elif match.match_format == MatchFormat.BO5:
            criteria_scores.append(0.9)
        elif match.match_format == MatchFormat.BO1:
            # Check for exceptional justification
            exceptional_score = self._evaluate_bo1_justification(favored_team, underdog_team, team_comparison)
            criteria_scores.append(exceptional_score)
            if exceptional_score < 0.8:
                red_flags.append("BO1 format without strong justification")
        
        # 4. Map pool advantage on at least 1 map
        map_advantage_score = self._evaluate_map_pool_advantage(
            favored_team, underdog_team,
            self.medium_risk_criteria['map_advantage_threshold'],
            self.medium_risk_criteria['map_advantage_count']
        )
        criteria_scores.append(map_advantage_score)
        
        # 5. Team average rating ≥ 1.05
        avg_rating = favored_team.average_rating or 0
        
        if avg_rating >= self.medium_risk_criteria['min_rating']:
            criteria_scores.append(1.0)
        else:
            criteria_scores.append(max(0, (avg_rating - 1.0) / 0.05))
        
        return sum(criteria_scores) / len(criteria_scores)
    
    def _evaluate_map_pool_advantage(
        self, 
        favored_team: TeamStats, 
        underdog_team: TeamStats,
        threshold: float,
        required_maps: int
    ) -> float:
        """Evaluate map pool advantage between teams"""
        
        if not favored_team.map_win_rates or not underdog_team.map_win_rates:
            return 0.0
        
        advantageous_maps = 0
        total_maps_compared = 0
        
        for map_name in favored_team.map_win_rates:
            if map_name in underdog_team.map_win_rates:
                favored_rate = favored_team.map_win_rates[map_name]
                underdog_rate = underdog_team.map_win_rates[map_name]
                
                total_maps_compared += 1
                
                if favored_rate >= threshold and underdog_rate <= 50:
                    advantageous_maps += 1
        
        if total_maps_compared == 0:
            return 0.0
        
        if advantageous_maps >= required_maps:
            return 1.0
        else:
            return advantageous_maps / required_maps
    
    def _evaluate_roster_stability(self, team1: TeamStats, team2: TeamStats) -> float:
        """Evaluate roster stability for both teams"""
        
        stability_scores = []
        
        for team in [team1, team2]:
            if not team.recent_roster_changes:
                stability_scores.append(1.0)
                continue
            
            recent_changes = 0
            cutoff_date = datetime.now() - timedelta(days=30)
            
            for change in team.recent_roster_changes:
                change_date = change.get('date')
                if change_date and isinstance(change_date, datetime):
                    if change_date > cutoff_date:
                        recent_changes += 1
            
            # Penalize recent changes
            if recent_changes == 0:
                stability_scores.append(1.0)
            elif recent_changes == 1:
                stability_scores.append(0.7)
            else:
                stability_scores.append(0.3)
        
        return min(stability_scores)  # Return the worst stability score
    
    def _evaluate_bo1_justification(
        self, 
        favored_team: TeamStats, 
        underdog_team: TeamStats,
        team_comparison: TeamComparison
    ) -> float:
        """Evaluate if BO1 format has exceptional justification"""
        
        justification_factors = []
        
        # Very large ranking gap (≥ 15)
        ranking_gap = team_comparison.ranking_gap or 0
        if ranking_gap >= 15:
            justification_factors.append(1.0)
        else:
            justification_factors.append(0.0)
        
        # Extremely high win rate difference (≥ 30%)
        win_rate_diff = team_comparison.win_rate_difference or 0
        if win_rate_diff >= 30:
            justification_factors.append(1.0)
        else:
            justification_factors.append(0.0)
        
        # Strong recent form advantage
        favored_form = favored_team.recent_form_trend
        underdog_form = underdog_team.recent_form_trend
        
        if favored_form == "improving" and underdog_form == "declining":
            justification_factors.append(1.0)
        else:
            justification_factors.append(0.0)
        
        # Return average of justification factors
        if justification_factors:
            return sum(justification_factors) / len(justification_factors)
        else:
            return 0.0
