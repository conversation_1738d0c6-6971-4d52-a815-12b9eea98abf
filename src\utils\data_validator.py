"""
Data validation utilities for CS2 betting analysis
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from src.models.match import Match, MatchPrediction
from src.models.team import TeamStats
from src.utils.logger import get_logger

logger = get_logger("DataValidator")


class DataValidator:
    """Validates data quality and completeness for betting analysis"""
    
    @staticmethod
    def validate_match_data(match: Match) -> Dict[str, Any]:
        """
        Validate match data completeness and quality
        
        Returns:
            Dict with validation results and missing data points
        """
        validation_result = {
            "is_valid": True,
            "missing_critical": [],
            "missing_optional": [],
            "warnings": []
        }
        
        # Critical data points
        critical_fields = [
            ("team1", match.team1),
            ("team2", match.team2),
            ("tournament", match.tournament),
            ("scheduled_time", match.scheduled_time),
            ("match_format", match.match_format)
        ]
        
        for field_name, field_value in critical_fields:
            if not field_value:
                validation_result["missing_critical"].append(field_name)
                validation_result["is_valid"] = False
        
        # Optional but important fields
        optional_fields = [
            ("team1_hltv_id", match.team1_hltv_id),
            ("team2_hltv_id", match.team2_hltv_id),
            ("odds", match.odds)
        ]
        
        for field_name, field_value in optional_fields:
            if not field_value:
                validation_result["missing_optional"].append(field_name)
        
        # Time validation
        if match.scheduled_time:
            now = datetime.now()
            if match.scheduled_time < now - timedelta(hours=1):
                validation_result["warnings"].append("Match appears to be in the past")
            elif match.scheduled_time > now + timedelta(days=7):
                validation_result["warnings"].append("Match is more than 7 days in the future")
        
        return validation_result
    
    @staticmethod
    def validate_team_stats(team_stats: TeamStats) -> Dict[str, Any]:
        """
        Validate team statistics completeness
        
        Returns:
            Dict with validation results
        """
        validation_result = {
            "is_valid": True,
            "missing_critical": [],
            "missing_optional": [],
            "data_quality_score": 0.0
        }
        
        # Critical fields for analysis
        critical_fields = [
            ("name", team_stats.name),
            ("hltv_ranking", team_stats.hltv_ranking)
        ]
        
        for field_name, field_value in critical_fields:
            if not field_value:
                validation_result["missing_critical"].append(field_name)
                validation_result["is_valid"] = False
        
        # Important fields for quality analysis
        important_fields = [
            ("overall_win_rate", team_stats.overall_win_rate),
            ("recent_win_rate", team_stats.recent_win_rate),
            ("average_rating", team_stats.average_rating),
            ("map_win_rates", team_stats.map_win_rates),
            ("last_10_matches", team_stats.last_10_matches)
        ]
        
        available_important = 0
        for field_name, field_value in important_fields:
            if field_value:
                available_important += 1
            else:
                validation_result["missing_optional"].append(field_name)
        
        # Calculate data quality score
        validation_result["data_quality_score"] = available_important / len(important_fields)
        
        # Check data freshness
        if team_stats.last_updated:
            age_hours = (datetime.now() - team_stats.last_updated).total_seconds() / 3600
            if age_hours > 24:
                validation_result["warnings"] = [f"Data is {age_hours:.1f} hours old"]
        
        return validation_result
    
    @staticmethod
    def validate_prediction_quality(prediction: MatchPrediction) -> Dict[str, Any]:
        """
        Validate the quality of a betting prediction
        
        Returns:
            Dict with validation results
        """
        validation_result = {
            "is_valid": True,
            "quality_score": 0.0,
            "issues": []
        }
        
        quality_factors = []
        
        # Confidence level check
        if prediction.confidence_percentage >= 70:
            quality_factors.append(1.0)
        elif prediction.confidence_percentage >= 60:
            quality_factors.append(0.7)
        else:
            quality_factors.append(0.3)
            validation_result["issues"].append("Low confidence percentage")
        
        # Expected value check
        if prediction.expected_value > 0.1:
            quality_factors.append(1.0)
        elif prediction.expected_value > 0:
            quality_factors.append(0.5)
        else:
            quality_factors.append(0.0)
            validation_result["issues"].append("Negative or zero expected value")
            validation_result["is_valid"] = False
        
        # Statistical backing check
        backing_score = 0
        if prediction.ranking_gap:
            backing_score += 0.3
        if prediction.win_rate_difference:
            backing_score += 0.3
        if prediction.map_pool_advantage:
            backing_score += 0.2
        if prediction.recent_form_analysis:
            backing_score += 0.2
        
        quality_factors.append(backing_score)
        
        if backing_score < 0.5:
            validation_result["issues"].append("Insufficient statistical backing")
        
        # Red flags check
        if len(prediction.red_flags) > 2:
            quality_factors.append(0.3)
            validation_result["issues"].append("Too many red flags")
        elif len(prediction.red_flags) > 0:
            quality_factors.append(0.7)
        else:
            quality_factors.append(1.0)
        
        # Calculate overall quality score
        validation_result["quality_score"] = sum(quality_factors) / len(quality_factors)
        
        return validation_result
    
    @staticmethod
    def get_data_sufficiency_report(matches: List[Match], team_stats: Dict[str, TeamStats]) -> Dict[str, Any]:
        """
        Generate a comprehensive data sufficiency report
        
        Returns:
            Dict with overall data quality assessment
        """
        report = {
            "total_matches": len(matches),
            "matches_with_sufficient_data": 0,
            "teams_with_sufficient_data": 0,
            "overall_data_quality": 0.0,
            "recommendations": []
        }
        
        # Validate matches
        valid_matches = 0
        for match in matches:
            validation = DataValidator.validate_match_data(match)
            if validation["is_valid"]:
                valid_matches += 1
        
        report["matches_with_sufficient_data"] = valid_matches
        
        # Validate team stats
        valid_teams = 0
        total_quality_score = 0.0
        
        for team_name, stats in team_stats.items():
            validation = DataValidator.validate_team_stats(stats)
            if validation["is_valid"] and validation["data_quality_score"] >= 0.6:
                valid_teams += 1
            total_quality_score += validation["data_quality_score"]
        
        report["teams_with_sufficient_data"] = valid_teams
        
        if team_stats:
            report["overall_data_quality"] = total_quality_score / len(team_stats)
        
        # Generate recommendations
        if report["overall_data_quality"] < 0.5:
            report["recommendations"].append("Consider using additional data sources")
        
        if valid_matches < len(matches) * 0.8:
            report["recommendations"].append("Many matches lack critical data - consider filtering")
        
        if valid_teams < len(team_stats) * 0.7:
            report["recommendations"].append("Team statistics are incomplete - fetch additional data")
        
        logger.info(f"Data sufficiency report: {report}")
        
        return report
