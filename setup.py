"""
Setup script for CS2 Betting Analysis System
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False


def setup_environment():
    """Set up the development environment"""
    print("CS2 Betting Analysis System - Environment Setup")
    print("=" * 50)
    
    # Create necessary directories
    directories = [
        "logs",
        "data",
        "config",
        "tests"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    # Install Python dependencies
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        return False
    
    # Install HLTV stats library from GitHub
    if not run_command(
        "pip install git+https://github.com/researchersec/hltv-stats.git",
        "Installing HLTV stats library"
    ):
        print("⚠ HLTV stats library installation failed - continuing without it")
    
    # Copy environment file
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            run_command("cp .env.example .env", "Creating environment file")
            print("⚠ Please edit .env file with your API keys")
        else:
            print("⚠ .env.example not found")
    
    # Set up FlareSolverr with Docker
    print("\nSetting up FlareSolverr...")
    if run_command("docker --version", "Checking Docker installation"):
        if run_command("docker-compose up -d flaresolverr", "Starting FlareSolverr container"):
            print("✓ FlareSolverr is running on http://localhost:8191")
        else:
            print("⚠ FlareSolverr setup failed - you may need to start it manually")
    else:
        print("⚠ Docker not found - FlareSolverr will not be available")
        print("  Install Docker and run: docker-compose up -d flaresolverr")
    
    print("\n" + "=" * 50)
    print("Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file with your API keys")
    print("2. Test the installation: python main.py")
    print("3. Check logs in the logs/ directory")
    
    return True


def test_installation():
    """Test the installation"""
    print("\nTesting installation...")
    
    try:
        # Test imports
        from config.settings import settings
        from src.utils.logger import get_logger
        from src.models.match import Match
        
        print("✓ All imports successful")
        
        # Test configuration
        print(f"✓ Configuration loaded (log level: {settings.log_level})")
        
        # Test logger
        logger = get_logger("SetupTest")
        logger.info("Test log message")
        print("✓ Logger working")
        
        return True
        
    except Exception as e:
        print(f"✗ Installation test failed: {e}")
        return False


if __name__ == "__main__":
    if setup_environment():
        if test_installation():
            print("\n🎉 CS2 Betting Analysis System is ready to use!")
        else:
            print("\n❌ Installation test failed - please check the errors above")
            sys.exit(1)
    else:
        print("\n❌ Setup failed - please check the errors above")
        sys.exit(1)
