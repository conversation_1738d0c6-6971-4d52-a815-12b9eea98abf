#!/usr/bin/env python3
"""
Quick launcher script for CS2 Betting Analysis System
Provides easy access to common analysis scenarios
"""
import subprocess
import sys
from datetime import datetime, timedelta


def print_banner():
    """Print system banner"""
    print("🎯 CS2 Betting Analysis System")
    print("=" * 50)
    print("Select analysis option:")
    print()


def print_menu():
    """Print menu options"""
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    print("1. Analyze matches for TODAY")
    print("2. Analyze matches for TOMORROW")
    print(f"3. Analyze matches for CUSTOM DATE")
    print("4. Run DEMO with mock data")
    print("5. Show HELP")
    print("6. Exit")
    print()


def run_command(command):
    """Run command and handle errors"""
    try:
        result = subprocess.run(command, shell=True, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed with exit code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        return False


def get_custom_date():
    """Get custom date from user"""
    while True:
        date_input = input("Enter date (YYYY-MM-DD) or 'back' to return: ").strip()
        
        if date_input.lower() == 'back':
            return None
        
        try:
            # Validate date format
            datetime.strptime(date_input, '%Y-%m-%d')
            return date_input
        except ValueError:
            print("❌ Invalid date format. Please use YYYY-MM-DD format.")


def main():
    """Main menu loop"""
    while True:
        print_banner()
        print_menu()
        
        try:
            choice = input("Enter your choice (1-6): ").strip()
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)
        
        if choice == '1':
            print("\n🔄 Running analysis for TODAY...")
            run_command("python main.py")
            
        elif choice == '2':
            print("\n🔄 Running analysis for TOMORROW...")
            run_command("python main.py --tomorrow")
            
        elif choice == '3':
            custom_date = get_custom_date()
            if custom_date:
                print(f"\n🔄 Running analysis for {custom_date}...")
                run_command(f"python main.py --date {custom_date}")
            
        elif choice == '4':
            print("\n🔄 Running DEMO with mock data...")
            run_command("python demo.py")
            
        elif choice == '5':
            print("\n📖 HELP - Available Commands:")
            print()
            print("Direct command line usage:")
            print("  python main.py                    # Today's matches")
            print("  python main.py --tomorrow         # Tomorrow's matches")
            print("  python main.py --date 2025-05-26 # Specific date")
            print("  python main.py --verbose          # Detailed output")
            print("  python demo.py                    # Demo with mock data")
            print()
            print("Configuration:")
            print("  Edit .env file to add API keys")
            print("  Run 'docker-compose up -d flaresolverr' for HLTV scraping")
            print()
            
        elif choice == '6':
            print("\n👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-6.")
        
        if choice in ['1', '2', '3', '4']:
            input("\nPress Enter to continue...")
        print()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
