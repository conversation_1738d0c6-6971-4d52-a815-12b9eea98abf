#!/usr/bin/env python3
"""
Debug script for HLTV scraper to see HTML structure
"""
import asyncio
import aiohttp
from datetime import datetime
from bs4 import BeautifulSoup

async def debug_hltv():
    target_date = datetime(2025, 5, 26)
    
    # Format date for HLTV URL
    date_str = target_date.strftime("%Y-%m-%d")
    matches_url = f"https://www.hltv.org/matches?date={date_str}"
    
    print(f'Fetching: {matches_url}')
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(matches_url, headers=headers) as response:
                if response.status == 200:
                    html_content = await response.text()
                    
                    # Save HTML to file for inspection
                    with open('hltv_debug.html', 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    
                    print(f'HTML saved to hltv_debug.html (length: {len(html_content)} chars)')
                    
                    # Parse with BeautifulSoup
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # Look for team links
                    team_links = soup.find_all('a', href=lambda x: x and '/team/' in str(x))
                    print(f'Found {len(team_links)} team links:')
                    
                    for i, link in enumerate(team_links[:20]):
                        team_name = link.get_text().strip()
                        href = link.get('href', '')
                        print(f'  {i+1}. "{team_name}" -> {href}')
                    
                    # Look for time patterns
                    import re
                    time_pattern = r'\b(\d{1,2}:\d{2})\b'
                    times = re.findall(time_pattern, html_content)
                    print(f'\nFound {len(times)} time patterns: {times[:10]}')
                    
                    # Look for specific team names from screenshot
                    known_teams = ['9INE', 'Astrum', 'PARIVISION', 'SaShi', 'HEROIC Academy']
                    for team in known_teams:
                        if team in html_content:
                            print(f'Found team "{team}" in HTML')
                        else:
                            print(f'Team "{team}" NOT found in HTML')
                    
                else:
                    print(f'HTTP Error: {response.status}')
                    
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_hltv())
