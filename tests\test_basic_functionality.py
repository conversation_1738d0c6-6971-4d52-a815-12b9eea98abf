"""
Basic functionality tests for CS2 Betting Analysis System
"""
import pytest
from datetime import datetime
from src.models.match import Match, MatchFormat, RiskLevel
from src.models.team import TeamStats, PlayerStats
from src.utils.data_validator import DataValidator
from src.analysis.risk_assessment import RiskAssessment


class TestDataModels:
    """Test data model functionality"""
    
    def test_match_creation(self):
        """Test Match model creation"""
        match = Match(
            match_id="test_match_1",
            team1="Team Liquid",
            team2="NAVI",
            tournament="Test Tournament",
            scheduled_time=datetime.now(),
            match_format=MatchFormat.BO3
        )
        
        assert match.team1 == "Team Liquid"
        assert match.team2 == "NAVI"
        assert match.match_format == MatchFormat.BO3
        assert "test_match_1" in match.match_id
    
    def test_team_stats_creation(self):
        """Test TeamStats model creation"""
        player = PlayerStats(
            name="s1mple",
            rating_2_0=1.25,
            adr=85.5
        )
        
        team = TeamStats(
            name="NAVI",
            hltv_ranking=1,
            overall_win_rate=75.0,
            players=[player]
        )
        
        assert team.name == "NAVI"
        assert team.hltv_ranking == 1
        assert len(team.players) == 1
        assert team.players[0].name == "s1mple"


class TestDataValidator:
    """Test data validation functionality"""
    
    def test_match_validation_valid(self):
        """Test validation of valid match data"""
        match = Match(
            match_id="test_match_1",
            team1="Team Liquid",
            team2="NAVI",
            tournament="Test Tournament",
            scheduled_time=datetime.now(),
            match_format=MatchFormat.BO3
        )
        
        validator = DataValidator()
        result = validator.validate_match_data(match)
        
        assert result["is_valid"] is True
        assert len(result["missing_critical"]) == 0
    
    def test_match_validation_invalid(self):
        """Test validation of invalid match data"""
        match = Match(
            match_id="test_match_1",
            team1="",  # Missing team name
            team2="NAVI",
            tournament="",  # Missing tournament
            scheduled_time=datetime.now(),
            match_format=MatchFormat.BO3
        )
        
        validator = DataValidator()
        result = validator.validate_match_data(match)
        
        assert result["is_valid"] is False
        assert "team1" in result["missing_critical"]
        assert "tournament" in result["missing_critical"]
    
    def test_team_stats_validation(self):
        """Test team statistics validation"""
        team = TeamStats(
            name="NAVI",
            hltv_ranking=1,
            overall_win_rate=75.0,
            recent_win_rate=80.0,
            average_rating=1.15
        )
        
        validator = DataValidator()
        result = validator.validate_team_stats(team)
        
        assert result["is_valid"] is True
        assert result["data_quality_score"] > 0.5


class TestRiskAssessment:
    """Test risk assessment functionality"""
    
    def test_risk_assessment_initialization(self):
        """Test risk assessment initialization"""
        risk_assessor = RiskAssessment()
        
        assert risk_assessor.low_risk_criteria["ranking_gap"] >= 5
        assert risk_assessor.medium_risk_criteria["win_rate"] >= 50
    
    def test_map_pool_advantage_calculation(self):
        """Test map pool advantage calculation"""
        risk_assessor = RiskAssessment()
        
        # Create mock team stats
        favored_team = TeamStats(
            name="Team A",
            map_win_rates={
                "dust2": 75.0,
                "mirage": 80.0,
                "inferno": 70.0
            }
        )
        
        underdog_team = TeamStats(
            name="Team B",
            map_win_rates={
                "dust2": 45.0,
                "mirage": 40.0,
                "inferno": 55.0
            }
        )
        
        advantage_score = risk_assessor._evaluate_map_pool_advantage(
            favored_team, underdog_team, 65, 2
        )
        
        # Should find advantage on dust2 and mirage
        assert advantage_score == 1.0
    
    def test_roster_stability_evaluation(self):
        """Test roster stability evaluation"""
        risk_assessor = RiskAssessment()
        
        # Team with no recent changes
        stable_team = TeamStats(
            name="Stable Team",
            recent_roster_changes=[]
        )
        
        # Team with recent changes
        unstable_team = TeamStats(
            name="Unstable Team",
            recent_roster_changes=[
                {"date": datetime.now(), "type": "addition", "player": "new_player"}
            ]
        )
        
        stability_score = risk_assessor._evaluate_roster_stability(stable_team, unstable_team)
        
        # Should be penalized for recent changes
        assert stability_score < 1.0


class TestConfiguration:
    """Test configuration and settings"""
    
    def test_settings_import(self):
        """Test that settings can be imported and have expected values"""
        from config.settings import settings
        
        assert hasattr(settings, 'target_recommendations')
        assert hasattr(settings, 'min_confidence_threshold')
        assert settings.target_recommendations > 0
        assert settings.min_confidence_threshold >= 0
    
    def test_data_source_priority(self):
        """Test data source priority configuration"""
        from config.settings import get_data_source_priority
        
        priorities = get_data_source_priority()
        
        assert isinstance(priorities, list)
        assert len(priorities) > 0
        assert all(isinstance(source, str) for source in priorities)


class TestUtilities:
    """Test utility functions"""
    
    def test_logger_creation(self):
        """Test logger creation"""
        from src.utils.logger import get_logger
        
        logger = get_logger("TestLogger")
        assert logger is not None
        
        # Test logging (should not raise exception)
        logger.info("Test log message")
        logger.warning("Test warning message")
    
    def test_data_sufficiency_report(self):
        """Test data sufficiency report generation"""
        validator = DataValidator()
        
        # Create test data
        matches = [
            Match(
                match_id="test_1",
                team1="Team A",
                team2="Team B",
                tournament="Test Tournament",
                scheduled_time=datetime.now(),
                match_format=MatchFormat.BO3
            )
        ]
        
        team_stats = {
            "Team A": TeamStats(
                name="Team A",
                hltv_ranking=1,
                overall_win_rate=75.0
            ),
            "Team B": TeamStats(
                name="Team B",
                hltv_ranking=5,
                overall_win_rate=65.0
            )
        }
        
        report = validator.get_data_sufficiency_report(matches, team_stats)
        
        assert "total_matches" in report
        assert "overall_data_quality" in report
        assert report["total_matches"] == 1
        assert isinstance(report["overall_data_quality"], float)


if __name__ == "__main__":
    pytest.main([__file__])
