["tests/test_basic_functionality.py::TestConfiguration::test_data_source_priority", "tests/test_basic_functionality.py::TestConfiguration::test_settings_import", "tests/test_basic_functionality.py::TestDataModels::test_match_creation", "tests/test_basic_functionality.py::TestDataModels::test_team_stats_creation", "tests/test_basic_functionality.py::TestDataValidator::test_match_validation_invalid", "tests/test_basic_functionality.py::TestDataValidator::test_match_validation_valid", "tests/test_basic_functionality.py::TestDataValidator::test_team_stats_validation", "tests/test_basic_functionality.py::TestRiskAssessment::test_map_pool_advantage_calculation", "tests/test_basic_functionality.py::TestRiskAssessment::test_risk_assessment_initialization", "tests/test_basic_functionality.py::TestRiskAssessment::test_roster_stability_evaluation", "tests/test_basic_functionality.py::TestUtilities::test_data_sufficiency_report", "tests/test_basic_functionality.py::TestUtilities::test_logger_creation"]